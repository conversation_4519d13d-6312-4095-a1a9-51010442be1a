<template>
  <teleport to="body">
    <div v-if="visible" class="minimal-modal-mask" @click.self="$emit('close')">
      <div class="minimal-modal">
        <slot />
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
})

defineEmits(['close'])
</script>

<style scoped>
.minimal-modal-mask {
  position: fixed;
  z-index: 1000;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.12);
  display: flex;
  align-items: center;
  justify-content: center;
}
.minimal-modal {
  background: #fff;
  border-radius: 10px;
  min-width: 320px;
  max-width: 90vw;
  padding: 2em 1.5em;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.12);
  animation: modalIn 0.18s cubic-bezier(.4,0,.2,1);
}
@keyframes modalIn {
  0% { transform: translateY(40px) scale(0.98); opacity: 0; }
  100% { transform: none; opacity: 1; }
}
</style> 