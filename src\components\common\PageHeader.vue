<template>
  <div v-if="shouldShowHeader" class="page-header">
    <div class="section-title">
      <div class="title-bar"></div>
      <h2>{{ pageTitle }}</h2>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const shouldShowHeader = computed(() => {
  return route.meta.showPageHeader === true
})

const pageTitle = computed(() => {
  return route.meta.title || ''
})
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.title-bar {
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #5b7cff 0%, #a685ff 100%);
  border-radius: 2px;
  margin-right: 12px;
}

.section-title h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}
</style>