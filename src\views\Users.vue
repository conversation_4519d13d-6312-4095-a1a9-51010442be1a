<script setup lang="ts">
import { ref } from 'vue'

// 模拟用户数据
const users = ref([
  { id: 1, name: '张三', email: '<PERSON><PERSON><PERSON>@example.com', role: '管理员' },
  { id: 2, name: '李四', email: '<EMAIL>', role: '编辑' },
  { id: 3, name: '王五', email: '<EMAIL>', role: '用户' }
])
</script>

<template>
  <div class="users">
    <h1>用户管理</h1>
    <div class="user-table">
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>姓名</th>
            <th>邮箱</th>
            <th>角色</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="user in users" :key="user.id">
            <td>{{ user.id }}</td>
            <td>{{ user.name }}</td>
            <td>{{ user.email }}</td>
            <td>{{ user.role }}</td>
            <td>
              <button class="edit-btn">编辑</button>
              <button class="delete-btn">删除</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
.users {
  padding: 20px;
}
.user-table {
  margin-top: 20px;
}
table {
  width: 100%;
  border-collapse: collapse;
}
th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
}
th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}
.edit-btn, .delete-btn {
  padding: 5px 10px;
  margin-right: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.edit-btn {
  background-color: #67c23a;
  color: white;
}
.delete-btn {
  background-color: #f56c6c;
  color: white;
}
</style>