<template>
  <div class="chart-card">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-value">
        <span class="value">{{ value }}</span>
        <span class="label">日均值</span>
      </div>
    </div>
    <div class="chart-container" ref="chartRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  value: {
    type: Number,
    required: true,
  },
  data: {
    type: Array,
    required: true,
  },
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    
    const option = {
      grid: {
        top: 20,
        right: 20,
        bottom: 30,
        left: 20,
        containLabel: false,
      },
      xAxis: {
        type: 'category',
        data: ['05-09', '05-10', '05-11', '05-12', '05-13', '05-14', '05-15'],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#b0b3c6',
          fontSize: 11,
          margin: 12,
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#f0f1f5',
            width: 1,
          },
        },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#b0b3c6',
          fontSize: 11,
        },
      },
      series: [
        {
          data: props.data,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#5b7cff',
            width: 3,
          },
          itemStyle: {
            color: '#5b7cff',
            borderColor: '#fff',
            borderWidth: 2,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(91, 124, 255, 0.3)' },
                { offset: 1, color: 'rgba(91, 124, 255, 0.05)' },
              ],
            },
          },
        },
      ],
    }
    
    chartInstance.setOption(option)
    
    // 响应式
    const resizeObserver = new ResizeObserver(() => {
      chartInstance?.resize()
    })
    resizeObserver.observe(chartRef.value)
  }
})

onUnmounted(() => {
  chartInstance?.dispose()
})
</script>

<style scoped>
.chart-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px 0 rgba(91,124,255,0.08);
  padding: 1.8rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(var(--minimal-glass-blur));
  border: 1px solid rgba(255,255,255,0.2);
  position: relative;
  overflow: hidden;
}

.chart-card:hover {
  box-shadow: 0 8px 40px 0 rgba(91,124,255,0.15);
  transform: translateY(-2px);
  border-color: rgba(166,133,255,0.3);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.2rem;
}

.chart-title {
  font-size: 1em;
  font-weight: 500;
  color: var(--minimal-text);
  margin: 0;
}

.chart-value {
  text-align: right;
}

.value {
  display: block;
  font-size: 1.8em;
  font-weight: 700;
  color: var(--minimal-primary-gradient);
  line-height: 1;
}

.label {
  font-size: 0.8em;
  color: #7a7e8a;
  margin-top: 2px;
}

.chart-container {
  height: 180px;
  width: 100%;
}
</style>