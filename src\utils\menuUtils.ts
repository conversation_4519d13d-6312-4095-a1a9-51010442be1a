import { createIconHTML } from '@/assets/icon'

// 菜单项接口
export interface MenuItem {
  key: string
  label: string
  path: string
  iconKey?: string  // 图标键名，由后端提供
  icon?: string     // 图标HTML，前端生成
  children?: MenuItem[]
}

// 后端菜单数据接口（不包含icon字段）
export interface BackendMenuItem {
  key: string
  label: string
  path: string
  iconKey?: string
  children?: BackendMenuItem[]
}

/**
 * 处理后端菜单数据，为每个菜单项生成图标HTML
 * @param backendMenus 后端返回的菜单数据
 * @returns 处理后的菜单数据
 */
export const processMenuData = (backendMenus: BackendMenuItem[]): MenuItem[] => {
  return backendMenus.map(menu => ({
    ...menu,
    icon: menu.iconKey ? createIconHTML(menu.iconKey) : undefined,
    children: menu.children ? processMenuData(menu.children) : undefined
  }))
}

/**
 * 模拟后端菜单数据（用于开发测试）
 */
export const mockBackendMenuData = (): BackendMenuItem[] => {
  return [
    {
      key: 'dashboard',
      label: '首页',
      path: '/',
      iconKey: 'dashboard'
    },
    {
      key: 'chat',
      label: '智库问答',
      path: '/chat',
      iconKey: 'chat'
    },
    {
      key: 'knowledge-plus',
      label: '智库+',
      path: '/knowledge-plus',
      iconKey: 'knowledgePlus',
      children: [
        {
          key: 'personal-knowledge',
          label: '个人知识库',
          path: '/knowledge/personal',
          iconKey: 'personalKnowledge'
        },
        {
          key: 'team-knowledge',
          label: '团队知识库',
          path: '/knowledge/team',
          iconKey: 'teamKnowledge'
        },
        {
          key: 'enterprise-knowledge',
          label: '企业知识库',
          path: '/knowledge/enterprise',
          iconKey: 'enterpriseKnowledge'
        }
      ]
    },
    {
      key: 'system-management',
      label: '系统管理',
      path: '/admin',
      iconKey: 'systemManagement',
      children: [
        {
          key: 'user-management',
          label: '用户管理',
          path: '/admin/users',
          iconKey: 'userManagement'
        },
        {
          key: 'role-management',
          label: '角色管理',
          path: '/admin/roles',
          iconKey: 'roleManagement'
        },
        {
          key: 'permission-management',
          label: '权限管理',
          path: '/admin/permissions',
          iconKey: 'permissionManagement'
        },
        {
          key: 'enterprise-category',
          label: '企业分类管理',
          path: '/admin/enterprise-category',
          iconKey: 'enterpriseCategory'
        },
        {
          key: 'system-log',
          label: '系统日志',
          path: '/admin/logs',
          iconKey: 'systemLog'
        }
      ]
    }
  ]
}
