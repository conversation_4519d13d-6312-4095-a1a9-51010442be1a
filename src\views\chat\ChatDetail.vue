<template>
  <div class="chat-detail-page">
    <!-- 顶部导航栏 -->
    <header class="chat-header">
      <div class="header-left">
        <button class="back-button" @click="goBack">
          <svg viewBox="0 0 24 24" width="18" height="18">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="currentColor"/>
          </svg>
          返回
        </button>
        <div class="logo-section">
          <img src="@/assets/十一建logo/logo.png" alt="十一建logo" class="header-logo" />
          <span class="logo-text">十一智库</span>
        </div>
        <h1 class="page-title">智库问答</h1>
      </div>
      <div class="header-right">
        <div class="user-avatar">
          <span>{{ username.charAt(0) }}</span>
        </div>
        <span class="user-name">{{ username }}</span>
      </div>
    </header>
    
    <div class="chat-detail-container">
      <!-- 左侧知识分类 -->
      <div class="knowledge-categories">
        <div class="category-header">
          <h3 class="category-title">知识库分类</h3>
          <div class="category-subtitle">选择相关领域获得更精准答案</div>
        </div>
        
        <!-- 外部知识 -->
        <div class="category-group">
          <div class="category-group-title">
            <div class="group-icon">🌐</div>
            <span class="group-name">外部知识</span>
          </div>
          <div class="category-items">
            <div 
              v-for="(item, index) in externalKnowledge" 
              :key="index"
              class="category-item"
              :class="{ active: selectedCategories.includes(item.id) }"
              @click="toggleCategory(item.id)"
            >
              <div class="item-dot"></div>
              {{ item.name }}
            </div>
          </div>
        </div>
        
        <!-- 内部知识 -->
        <div class="category-group">
          <div class="category-group-title">
            <div class="group-icon">🏢</div>
            <span class="group-name">内部知识</span>
          </div>
          <div class="category-items">
            <div 
              v-for="(item, index) in internalKnowledge" 
              :key="index"
              class="category-item"
              :class="{ active: selectedCategories.includes(item.id) }"
              @click="toggleCategory(item.id)"
            >
              <div class="item-dot"></div>
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧聊天区域 -->
      <div class="chat-area">
        <div class="chat-content" ref="chatContentRef">
          <div v-if="messages.length === 0" class="chat-welcome">
            <div class="welcome-container">
              <div class="welcome-icon">
                <img src="@/assets/十一建logo/1.gif" alt="智能助手" class="welcome-gif" />
              </div>
              <div class="welcome-text">
                <h3>欢迎使用智库问答</h3>
                <p>我可以帮助您解答各种问题，请在下方输入您的问题</p>
              </div>
            </div>
            
            <div class="quick-questions">
              <div class="quick-question" @click="askQuestion('如何查询现行法律规范？')">
                <div class="question-icon">⚖️</div>
                <span>如何查询现行法律规范？</span>
              </div>
              <div class="quick-question" @click="askQuestion('十一建有哪些管理办法？')">
                <div class="question-icon">📋</div>
                <span>十一建有哪些管理办法？</span>
              </div>
              <div class="quick-question" @click="askQuestion('有哪些施工指南可以参考？')">
                <div class="question-icon">🏗️</div>
                <span>有哪些施工指南可以参考？</span>
              </div>
            </div>
          </div>
          
          <!-- 消息列表 -->
          <div v-for="message in messages" :key="message.time" class="message" :class="message.type">
            <div class="message-avatar">
              <span v-if="message.type === 'user'">{{ username.charAt(0) }}</span>
              <div v-else class="user-avatar">{{ getCurrentUserAvatar() }}</div>
            </div>
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <!-- 显示消息中的文件 -->
              <div v-if="message.files && message.files.length > 0" class="message-files">
                <div v-for="file in message.files" :key="file.id" class="message-file">
                  <div class="file-icon">
                    <svg v-if="isImageFile(file)" viewBox="0 0 24 24" width="14" height="14">
                      <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" fill="currentColor"/>
                    </svg>
                    <svg v-else viewBox="0 0 24 24" width="14" height="14">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                    </svg>
                  </div>
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                </div>
              </div>
              <div class="message-time">{{ message.time }}</div>
            </div>
          </div>
          
          <!-- 打字指示器 -->
          <div v-if="isTyping" class="message assistant">
            <div class="message-avatar">
              <div class="ai-avatar">🤖</div>
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="chat-input">
          <div class="input-container">
            <div class="input-wrapper">
              <textarea 
                v-model="inputMessage" 
                placeholder="请输入您的问题..." 
                @keyup.enter.ctrl="sendMessage"
                rows="1"
                class="chat-textarea"
              ></textarea>
              <div class="input-actions">
                <button class="upload-btn" @click="triggerFileUpload" title="上传文件">
                  <svg viewBox="0 0 24 24" width="20" height="20">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                  </svg>
                </button>
                <button 
                  class="send-btn" 
                  @click="sendMessage"
                  :disabled="!inputMessage.trim() || isTyping"
                  title="发送消息"
                >
                  <svg viewBox="0 0 24 24" width="18" height="18">
                    <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z" fill="currentColor"/>
                  </svg>
                </button>
              </div>
            </div>
            <div class="input-tip">按 Ctrl + Enter 发送</div>
          </div>
          
          <!-- 隐藏的文件上传输入 -->
          <input 
            ref="fileInput" 
            type="file" 
            multiple 
            style="display: none" 
            @change="handleFileUpload"
            accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const username = ref('用户')
const inputMessage = ref('')
const isTyping = ref(false)
const messages = ref<any[]>([])
const selectedCategories = ref<string[]>([])
const chatContentRef = ref<HTMLElement>()
const fileInput = ref<HTMLInputElement>()

// 知识库数据
const externalKnowledge = ref([
  { id: 'law', name: '现行法律规范' },
  { id: 'industry', name: '行业信息网站' }
])

const internalKnowledge = ref([
  { id: 'management', name: '建设管理办法' },
  { id: 'construction', name: '十一建制度' },
  { id: 'standard', name: '十一建标准' },
  { id: 'guide', name: '十一建相关管理办法' },
  { id: 'construction-guide', name: '施工指南/指引/工艺' },
  { id: 'enterprise', name: '企业工艺标准' },
  { id: 'case', name: '案例资料' },
  { id: 'knowledge', name: '知识条理' }
])

// 方法
const goBack = () => {
  router.back()
}

const toggleCategory = (categoryId: string) => {
  const index = selectedCategories.value.indexOf(categoryId)
  if (index > -1) {
    selectedCategories.value.splice(index, 1)
  } else {
    selectedCategories.value.push(categoryId)
  }
}

const askQuestion = (question: string) => {
  inputMessage.value = question
  sendMessage()
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isTyping.value) return
  
  const userMessage = {
    type: 'user',
    content: inputMessage.value,
    time: new Date().toLocaleTimeString(),
    files: []
  }
  
  messages.value.push(userMessage)
  const question = inputMessage.value
  inputMessage.value = ''
  
  // 滚动到底部
  await nextTick()
  scrollToBottom()
  
  // 模拟AI回复
  isTyping.value = true
  setTimeout(() => {
    const aiMessage = {
      type: 'assistant',
      content: `关于"${question}"的问题，我正在为您查询相关资料...`,
      time: new Date().toLocaleTimeString(),
      files: []
    }
    messages.value.push(aiMessage)
    isTyping.value = false
    nextTick(() => scrollToBottom())
  }, 1500)
}

const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files && files.length > 0) {
    // 处理文件上传逻辑
    console.log('上传文件:', files)
  }
}

const isImageFile = (file: any) => {
  return file.type?.startsWith('image/')
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const scrollToBottom = () => {
  if (chatContentRef.value) {
    chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
  }
}

// 获取当前用户头像文字
function getCurrentUserAvatar(): string {
  const currentUsername = localStorage.getItem('currentUsername')
  if (currentUsername) {
    return currentUsername.charAt(0).toUpperCase()
  }
  
  const userRole = localStorage.getItem('userRole') || 'user'
  switch (userRole) {
    case 'admin':
      return '管'
    case 'staff':
      return '员'
    default:
      return '用'
  }
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style scoped>
.chat-detail-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background: var(--minimal-glass-bg);
  backdrop-filter: blur(var(--minimal-glass-blur));
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--minimal-shadow);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.back-button {
  background: var(--minimal-glass-bg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: var(--minimal-radius);
  color: var(--minimal-text);
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(91, 124, 255, 0.1);
  border-color: var(--minimal-primary-gradient);
  color: var(--minimal-primary-gradient);
  transform: translateY(-1px);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-logo {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo-text {
  font-size: 1rem;
  font-weight: 600;
  color: var(--minimal-text);
  background: var(--minimal-primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--minimal-text);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--minimal-primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(91, 124, 255, 0.3);
}

.user-name {
  font-weight: 500;
  color: var(--minimal-text);
  background: var(--minimal-glass-bg);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.chat-detail-container {
  background: linear-gradient(135deg, #f3f6fd 0%, #e9eafc 50%, #f7f8fa 100%);
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.knowledge-categories {
  width: 250px;
  background: var(--minimal-glass-bg);
  border-radius: var(--minimal-radius);
  padding: 1.5rem;
  box-shadow: var(--minimal-shadow);
  height: fit-content;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(var(--minimal-glass-blur));
}

.category-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(91, 124, 255, 0.1);
}

.category-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0 0 0.5rem 0;
}

.category-subtitle {
  font-size: 0.8rem;
  color: rgba(45, 55, 72, 0.6);
  line-height: 1.4;
}

.category-group {
  margin-bottom: 1.5rem;
}

.category-group:last-child {
  margin-bottom: 0;
}

.category-group-title {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  background: rgba(91, 124, 255, 0.05);
  border-radius: 0.75rem;
  border-left: 3px solid var(--minimal-primary-gradient);
}

.group-icon {
  font-size: 1.1rem;
  margin-right: 0.5rem;
}

.group-name {
  font-weight: 600;
  color: var(--minimal-text);
  font-size: 0.9rem;
}

.category-items {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding-left: 0.5rem;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  color: var(--minimal-text);
  background: transparent;
  border: 1px solid transparent;
  position: relative;
}

.item-dot {
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 50%;
  background: rgba(91, 124, 255, 0.4);
  margin-right: 0.75rem;
  transition: all 0.3s ease;
}

.category-item:hover {
  background: rgba(91, 124, 255, 0.08);
  color: var(--minimal-primary-gradient);
  transform: translateX(0.25rem);
}

.category-item:hover .item-dot {
  background: var(--minimal-primary-gradient);
  transform: scale(1.5);
}

.category-item.active {
  background: rgba(91, 124, 255, 0.15);
  color: var(--minimal-primary-gradient);
  font-weight: 500;
  border-color: rgba(91, 124, 255, 0.3);
}

.category-item.active .item-dot {
  background: var(--minimal-primary-gradient);
  transform: scale(1.5);
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--minimal-glass-bg);
  border-radius: var(--minimal-radius);
  box-shadow: var(--minimal-shadow);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(var(--minimal-glass-blur));
}

.chat-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  min-height: 400px;
}

.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
}

.welcome-icon {
  width: 8rem;
  height: 8rem;
  margin-bottom: 1.5rem;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(91, 124, 255, 0.2);
}

.welcome-gif {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.welcome-text h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--minimal-primary-gradient);
  margin-bottom: 0.75rem;
  background: var(--minimal-primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-text p {
  font-size: 0.9rem;
  color: rgba(45, 55, 72, 0.7);
  max-width: 400px;
  line-height: 1.6;
  margin: 0;
}

.quick-questions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  max-width: 900px;
  width: 100%;
}

.quick-question {
  display: flex;
  align-items: center;
  background: var(--minimal-glass-bg);
  padding: 1rem 1.25rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  color: var(--minimal-text);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.question-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.quick-question:hover {
  background: rgba(91, 124, 255, 0.1);
  color: var(--minimal-primary-gradient);
  border-color: rgba(91, 124, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(91, 124, 255, 0.15);
}

.message {
  display: flex;
  margin-bottom: 1.5rem;
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 1rem;
  flex-shrink: 0;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message.user .message-avatar {
  background: var(--minimal-primary-gradient);
  color: white;
}

.message.assistant .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-avatar {
  font-size: 1rem;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 3.5rem);
}

.message-text {
  padding: 1rem 1.25rem;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  max-width: 85%;
  line-height: 1.6;
  font-size: 0.9rem;
  word-wrap: break-word;
}

.message.user .message-text {
  background: var(--minimal-primary-gradient);
  color: white;
  border-top-right-radius: 0.25rem;
  margin-left: auto;
  box-shadow: 0 4px 12px rgba(91, 124, 255, 0.3);
}

.message.assistant .message-text {
  background: var(--minimal-glass-bg);
  color: var(--minimal-text);
  border-top-left-radius: 0.25rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.message-time {
  font-size: 0.7rem;
  color: rgba(45, 55, 72, 0.5);
  margin-top: 0.25rem;
  padding: 0 0.25rem;
}

.message.user .message-time {
  text-align: right;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 1rem 1.25rem;
  background: var(--minimal-glass-bg);
  border-radius: 1rem;
  width: fit-content;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.typing-indicator span {
  width: 0.5rem;
  height: 0.5rem;
  background: var(--minimal-primary-gradient);
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) { animation-delay: 0s; }
.typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
.typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1.2); opacity: 1; }
}

.chat-input {
  padding: 2rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(248, 250, 252, 0.5);
  backdrop-filter: blur(10px);
}

.input-container {
  max-width: 1000px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  border: 2px solid rgba(91, 124, 255, 0.3);
  border-radius: 1.5rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  backdrop-filter: blur(var(--minimal-glass-blur));
  min-height: 60px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.input-wrapper:focus-within {
  border-color: var(--minimal-primary-gradient);
  box-shadow: 0 0 0 4px rgba(91, 124, 255, 0.15), 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.input-wrapper:hover {
  border-color: rgba(91, 124, 255, 0.5);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.chat-textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 0.5rem 0;
  font-size: 1rem;
  resize: none;
  font-family: inherit;
  line-height: 1.5;
  max-height: 150px;
  min-height: 2rem;
  background: transparent;
  color: var(--minimal-text);
}

.chat-textarea::placeholder {
  color: rgba(45, 55, 72, 0.5);
  font-size: 1rem;
}

.input-actions {
  display: flex;
  align-items: center;
  margin-left: 1rem;
  gap: 0.75rem;
}

.upload-btn, .send-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.upload-btn {
  color: rgba(45, 55, 72, 0.6);
  width: 2.5rem;
  height: 2.5rem;
}

.upload-btn:hover {
  background: rgba(91, 124, 255, 0.1);
  color: var(--minimal-primary-gradient);
  transform: scale(1.1);
}

.send-btn {
  background: var(--minimal-primary-gradient);
  color: white;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(91, 124, 255, 0.3);
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(91, 124, 255, 0.4);
}

.send-btn:disabled {
  background: rgba(45, 55, 72, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.input-tip {
  text-align: center;
  font-size: 0.85rem;
  color: rgba(45, 55, 72, 0.5);
  margin-top: 1rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .chat-detail-container {
    flex-direction: column;
    padding: 1rem;
  }
  
  .knowledge-categories {
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .category-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
  }
  
  .quick-questions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chat-header {
    padding: 0.75rem 1rem;
  }
  
  .header-left {
    gap: 1rem;
  }
  
  .page-title {
    font-size: 1rem;
  }
  
  .logo-text {
    display: none;
  }
  
  .chat-content {
    padding: 1rem;
  }
  
  .chat-input {
    padding: 1rem;
  }
  
  .welcome-icon {
    width: 6rem;
    height: 6rem;
  }
  
  .welcome-text h3 {
    font-size: 1.25rem;
  }
}
</style>







