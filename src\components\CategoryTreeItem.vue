<template>
  <div class="category-tree-item">
    <!-- 当前项目 -->
    <div 
      class="category-item" 
      :style="{ paddingLeft: (level * 20 + 20) + 'px' }"
      @click="handleItemClick"
    >
      <div
        class="item-checkbox"
        :class="{
          checked: isSelected,
          indeterminate: isIndeterminate
        }"
        @click.stop="handleCheckboxClick"
      >
        <el-icon v-if="isSelected">
          <Check />
        </el-icon>
        <el-icon v-else-if="isIndeterminate">
          <Minus />
        </el-icon>
      </div>
      <span class="item-name">{{ item.name }}</span>
      <el-icon
        v-if="hasChildren"
        class="expand-icon"
        :class="{ expanded: isExpanded }"
        @click.stop="handleExpandClick"
      >
        <ArrowRight />
      </el-icon>
    </div>

    <!-- 递归渲染子项 -->
    <div v-if="isExpanded && hasChildren" class="children-container">
      <CategoryTreeItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
        :level="level + 1"
        :selected-knowledge="selectedKnowledge"
        :expanded-items="expandedItems"
        @toggle-selection="$emit('toggle-selection', $event)"
        @toggle-expand="$emit('toggle-expand', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElIcon } from 'element-plus'
import { Check, ArrowRight, Minus } from '@element-plus/icons-vue'

interface CategoryItem {
  id: string
  name: string
  children?: CategoryItem[]
}

interface Props {
  item: CategoryItem
  level: number
  selectedKnowledge: string[]
  expandedItems: string[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'toggle-selection': [item: CategoryItem]
  'toggle-expand': [itemId: string]
}>()

// 计算属性
const hasChildren = computed(() => props.item.children && props.item.children.length > 0)
const isExpanded = computed(() => props.expandedItems.includes(props.item.id))

const isSelected = computed(() => {
  if (!hasChildren.value) {
    return props.selectedKnowledge.includes(props.item.id)
  }
  return isAllChildrenSelected(props.item)
})

const isIndeterminate = computed(() => {
  if (!hasChildren.value) return false
  return !isAllChildrenSelected(props.item) && isSomeChildrenSelected(props.item)
})

// 辅助函数
const isAllChildrenSelected = (item: CategoryItem): boolean => {
  if (!item.children || item.children.length === 0) return false
  return item.children.every((child: CategoryItem) => {
    if (child.children && child.children.length > 0) {
      return isAllChildrenSelected(child)
    }
    return props.selectedKnowledge.includes(child.id)
  })
}

const isSomeChildrenSelected = (item: CategoryItem): boolean => {
  if (!item.children || item.children.length === 0) return false
  return item.children.some((child: CategoryItem) => {
    if (child.children && child.children.length > 0) {
      return isSomeChildrenSelected(child) || isAllChildrenSelected(child)
    }
    return props.selectedKnowledge.includes(child.id)
  })
}

// 事件处理
const handleItemClick = () => {
  if (hasChildren.value) {
    handleExpandClick()
  } else {
    handleCheckboxClick()
  }
}

const handleCheckboxClick = () => {
  emit('toggle-selection', props.item)
}

const handleExpandClick = () => {
  emit('toggle-expand', props.item.id)
}
</script>

<style scoped>
.category-tree-item {
  width: 100%;
}

.category-item {
  display: flex;
  align-items: center;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  border-bottom: 1px solid #f8fafc;
  white-space: nowrap;
  word-break: keep-all;
  padding: 8px 20px;
  min-height: 40px;
}

.category-item:hover {
  background-color: #f8fafc;
}

.item-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
  background: white;
  transition: all 0.2s ease;
}

.item-checkbox:hover {
  border-color: #4f46e5;
}

.item-checkbox.checked {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.item-checkbox.indeterminate {
  background: #f59e0b;
  border-color: #f59e0b;
  color: white;
}

.item-checkbox .el-icon {
  font-size: 10px;
  font-weight: bold;
}

.item-name {
  flex: 1;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expand-icon {
  margin-left: 8px;
  transition: transform 0.2s ease;
  cursor: pointer;
  flex-shrink: 0;
  color: #6b7280;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.children-container {
  width: 100%;
}

/* 根据层级调整样式 */
.category-item {
  font-size: calc(14px - var(--level, 0) * 1px);
  color: hsl(220, calc(13% + var(--level, 0) * 5%), calc(25% + var(--level, 0) * 10%));
}
</style>
