<script setup lang="ts">
import { ref } from 'vue'
import StatCard from '@/components/minimal-ui/StatCard.vue'
import ChartCard from '@/components/minimal-ui/ChartCard.vue'
import Copyright from '@/components/common/Copyright.vue'
import { ElDatePicker } from 'element-plus'

// 日期选择器
const dateRange = ref<[Date, Date]>([
  new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000), // 7天前
  new Date() // 今天
])

const stats = ref([
  {
    value: '122356',
    desc: '累计日活跃成员数',
    icon: `<svg viewBox="0 0 24 24" width="1em" height="1em"><path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" fill="currentColor"/></svg>`,
    color: '#3a7bd5'
  },
  {
    value: '25869',
    desc: '累计新建会话数',
    icon: `<svg viewBox="0 0 24 24" width="1em" height="1em"><circle cx="12" cy="12" r="3" fill="currentColor"/><circle cx="12" cy="12" r="8" stroke="currentColor" stroke-width="2" fill="none"/><circle cx="12" cy="12" r="1" fill="white"/></svg>`,
    color: '#409eff'
  },
  {
    value: '1223',
    desc: '用户提问数',
    icon: `<svg viewBox="0 0 24 24" width="1em" height="1em"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/><path d="M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2" fill="none"/><circle cx="12" cy="17" r="1" fill="currentColor"/></svg>`,
    color: '#67c23a'
  }
])

const chartData = ref([
  {
    title: '日活跃成员数',
    value: 123,
    data: [8, 6, 10, 15, 12, 9, 7, 6, 8, 5, 4, 3]
  },
  {
    title: '日新建会话数',
    value: 123,
    data: [5, 8, 12, 16, 14, 10, 6, 4, 7, 9, 11, 8]
  },
  {
    title: '日文档助手',
    value: 123,
    data: [10, 8, 6, 12, 15, 11, 9, 7, 5, 8, 10, 6]
  },
  {
    title: '日图表查询',
    value: 123,
    data: [6, 9, 11, 8, 10, 13, 15, 12, 8, 6, 4, 7]
  },
  {
    title: '日成果匹配',
    value: 89,
    data: [4, 7, 9, 12, 8, 6, 10, 14, 11, 7, 5, 9]
  },
  {
    title: '日翻译助手',
    value: 156,
    data: [12, 15, 18, 14, 16, 20, 17, 13, 19, 16, 14, 18]
  }
])
</script>
<template>
  <div class="dashboard-page">
    <div class="dashboard-content">
      <!-- 数据总览 -->
      <div class="section-title section-title-with-date">
        <div class="title-left">
          <div class="title-bar"></div>
          <h2>数据总览</h2>
        </div>
        <div class="date-picker-container">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            size="default"
          />
        </div>
      </div>
      <div class="dashboard-stats">
        <StatCard 
          v-for="(stat, i) in stats" 
          :key="i" 
          :value="stat.value" 
          :desc="stat.desc"
          :color="stat.color"
        >
          <template #icon><span v-html="stat.icon" /></template>
        </StatCard>
      </div>
      
      <!-- 数据趋势 -->
      <div class="section-title">
        <div class="title-bar"></div>
        <h2>数据趋势</h2>
      </div>
      <div class="dashboard-charts">
        <ChartCard 
          v-for="(chart, i) in chartData" 
          :key="i"
          :title="chart.title"
          :value="chart.value"
          :data="chart.data"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-page {
  padding: 1rem 1rem;
  background: var(--minimal-bg);
}

.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 2rem 0 1.5rem 0;
}

.section-title-with-date {
  justify-content: space-between;
}

.title-left {
  display: flex;
  align-items: center;
}

.date-picker-container {
  margin-left: auto;
}

.section-title:first-child {
  margin-top: 0;
}

.title-bar {
  width: 4px;
  height: 20px;
  background: var(--minimal-primary-gradient);
  border-radius: 2px;
  margin-right: 12px;
}

.section-title h2 {
  font-size: 1.3em;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .dashboard-page {
    padding: 1.5rem;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .dashboard-charts {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
