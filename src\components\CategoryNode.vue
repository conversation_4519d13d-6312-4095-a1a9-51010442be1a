<template>
  <div class="category-node" :style="{ paddingLeft: level * 16 + 'px' }">
    <div
      class="node-item"
      :class="{
        active: selectedId === node.id,
        'has-children': node.children && node.children.length > 0,
        [`level-${level}`]: true
      }"
    >
      <!-- 展开/收起图标 -->
      <div
        v-if="node.children && node.children.length > 0"
        class="expand-toggle"
        @click="$emit('toggle', node.id)"
      >
        <el-icon :class="{ expanded: expandedIds.includes(node.id) }">
          <ArrowRight />
        </el-icon>
      </div>
      <div v-else class="expand-placeholder"></div>

      <!-- 节点内容 -->
      <div
        class="node-content"
        @click="$emit('select', node.id)"
      >
        <div class="node-checkbox">
          <el-icon v-if="selectedId === node.id">
            <Check />
          </el-icon>
        </div>
        <div class="level-indicator" :class="`level-${level}`" v-if="level > 0"></div>
        <span class="node-text">{{ node.name }}</span>
      </div>
    </div>

    <!-- 子节点 -->
    <div v-if="node.children && expandedIds.includes(node.id)" class="children-nodes">
      <CategoryNode
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :level="level + 1"
        :selected-id="selectedId"
        :expanded-ids="expandedIds"
        @select="$emit('select', $event)"
        @toggle="$emit('toggle', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElIcon } from 'element-plus'
import { Check, ArrowRight } from '@element-plus/icons-vue'

defineProps({
  node: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  },
  selectedId: {
    type: String,
    default: ''
  },
  expandedIds: {
    type: Array,
    default: () => []
  }
})

defineEmits(['select', 'toggle'])
</script>

<style scoped>
/* CategoryNode 组件样式 */
.category-node {
  margin-bottom: 1px;
}

.node-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

/* 不同层级的样式 */
.node-item.level-0 {
  background: linear-gradient(135deg, #c7d2fe 0%, #a5b4fc 100%);
  color: #4338ca;
  font-weight: 600;
  font-size: 15px;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.node-item.level-1 {
  background: #f8fafc;
  border-left: 3px solid #e2e8f0;
  font-weight: 500;
  font-size: 14px;
  padding: 8px 12px;
}

.node-item.level-2 {
  background: #fefefe;
  border-left: 2px solid #f1f5f9;
  font-size: 13px;
  padding: 6px 10px;
}

.node-item.level-3,
.node-item.level-4,
.node-item.level-5 {
  background: transparent;
  font-size: 12px;
  padding: 4px 8px;
  color: #64748b;
}

.node-item:hover {
  background: #f1f5f9 !important;
  transform: translateX(2px);
}

.node-item.level-0:hover {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%) !important;
  transform: translateX(0);
}

.node-item.active {
  background: #e0e7ff !important;
  color: #4f46e5 !important;
  border-left-color: #4f46e5;
}

.node-item.level-0.active {
  background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%) !important;
}

.expand-toggle {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.2s ease;
  color: #6b7280;
  flex-shrink: 0;
}

.expand-toggle:hover {
  background: rgba(107, 114, 128, 0.15);
  color: #374151;
}

.expand-toggle .el-icon {
  transition: transform 0.2s ease;
  font-size: 12px;
}

.expand-toggle .el-icon.expanded {
  transform: rotate(90deg);
}

.expand-placeholder {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  min-width: 0;
}

.level-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.level-indicator.level-1 {
  background: #f59e0b;
}

.level-indicator.level-2 {
  background: #3b82f6;
}

.level-indicator.level-3 {
  background: #10b981;
}

.level-indicator.level-4 {
  background: #ef4444;
}

.level-indicator.level-5 {
  background: #8b5a2b;
}

.node-checkbox {
  width: 14px;
  height: 14px;
  border: 1.5px solid #d1d5db;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
  background: white;
}

/* 顶级节点的复选框样式 */
.node-item.level-0 .node-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #c7d2fe;
  border-radius: 4px;
}

.node-item.active .node-checkbox {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.node-item.level-0.active .node-checkbox {
  background: #4338ca;
  border-color: #4338ca;
  box-shadow: 0 0 0 2px rgba(67, 56, 202, 0.2);
}

.node-checkbox .el-icon {
  font-size: 8px;
  font-weight: bold;
}

.node-item.level-0 .node-checkbox .el-icon {
  font-size: 10px;
}

.node-text {
  flex: 1;
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.4;
  min-width: 0;
}

.children-nodes {
  margin-top: 2px;
}

/* 层级连接线已移除，避免视觉混乱 */
</style>
