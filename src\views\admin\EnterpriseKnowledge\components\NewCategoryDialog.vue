<template>
  <el-dialog
    v-model="visible"
    title="新增分类"
    width="420px"
    :before-close="handleCancel"
    class="minimal-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <div class="dialog-icon">
          <FolderAdd />
        </div>
        <h3>新增分类</h3>
      </div>
    </template>

    <div class="dialog-content">
      <el-form :model="formData" label-width="80px" class="minimal-form">
        <el-form-item label="分类名称" required>
          <el-input
            v-model="formData.name"
            placeholder="请输入分类名称"
            maxlength="50"
            show-word-limit
            class="minimal-input"
          />
        </el-form-item>
        <el-form-item label="父分类">
          <el-select
            v-model="formData.parentId"
            placeholder="请选择父分类（留空则创建根分类）"
            clearable
            class="minimal-select"
            style="width: 100%"
          >
            <el-option
              v-for="option in categoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-actions">
        <el-button @click="handleCancel" class="minimal-btn minimal-btn--secondary">
          取消
        </el-button>
        <el-button type="primary" @click="handleConfirm" class="minimal-btn minimal-btn--primary">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { FolderAdd } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  formData: {
    name: string
    parentId: string
    parentName: string
  }
  categoryOptions: any[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'update:formData', value: any): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formData = computed({
  get: () => props.formData,
  set: (value) => emit('update:formData', value)
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
/* 极简对话框样式 */
:deep(.minimal-dialog .el-dialog) {
  background: var(--minimal-glass-bg);
  border-radius: var(--minimal-radius);
  box-shadow: 0 12px 48px 0 rgba(91, 124, 255, 0.15);
  backdrop-filter: blur(var(--minimal-glass-blur));
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

:deep(.minimal-dialog .el-dialog__header) {
  padding: 0;
  margin: 0;
}

:deep(.minimal-dialog .el-dialog__body) {
  padding: 0;
}

:deep(.minimal-dialog .el-dialog__footer) {
  padding: 0;
}

.dialog-header {
  display: flex;
  align-items: center;
  padding: 2em 2.5em 1em 2.5em;
  gap: 1em;
}

.dialog-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--minimal-primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px 0 rgba(91, 124, 255, 0.3);
}

.dialog-header h3 {
  font-size: 1.25em;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0;
  letter-spacing: 0.01em;
}

.dialog-content {
  padding: 0 2.5em 1.5em 2.5em;
}

.dialog-actions {
  display: flex;
  gap: 1em;
  padding: 1.5em 2.5em 2em 2.5em;
  justify-content: flex-end;
}

.minimal-btn {
  padding: 0.75em 1.5em;
  border-radius: var(--minimal-radius);
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.minimal-btn--secondary {
  background: rgba(107, 114, 128, 0.1);
  color: var(--minimal-text-secondary);
}

.minimal-btn--secondary:hover {
  background: rgba(107, 114, 128, 0.2);
}

.minimal-btn--primary {
  background: var(--minimal-primary-gradient);
  color: white;
  box-shadow: 0 4px 12px 0 rgba(91, 124, 255, 0.3);
}

.minimal-btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(91, 124, 255, 0.4);
}

@media (max-width: 480px) {
  :deep(.minimal-dialog .el-dialog) {
    width: 90% !important;
    margin: 1rem;
  }

  .dialog-header,
  .dialog-content,
  .dialog-actions {
    padding-left: 1.5em;
    padding-right: 1.5em;
  }

  .dialog-actions {
    flex-direction: column;
  }

  .minimal-btn {
    width: 100%;
  }
}
</style>
