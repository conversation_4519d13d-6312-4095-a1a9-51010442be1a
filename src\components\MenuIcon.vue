<template>
  <img 
    v-if="iconUrl" 
    :src="iconUrl" 
    :alt="iconKey"
    class="menu-icon"
  />
  <span v-else class="menu-icon-fallback">{{ iconKey }}</span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getMenuIcon, type MenuIconKey } from '@/assets/icon'

interface Props {
  iconKey: string
}

const props = defineProps<Props>()

// 动态获取图标URL
const iconUrl = computed(() => {
  try {
    return getMenuIcon(props.iconKey as MenuIconKey)
  } catch (error) {
    console.warn(`图标 ${props.iconKey} 不存在`)
    return null
  }
})
</script>

<style scoped>
.menu-icon {
  width: 1em;
  height: 1em;
  display: inline-block;
  vertical-align: middle;
}

.menu-icon-fallback {
  display: inline-block;
  width: 1em;
  height: 1em;
  background: #ccc;
  border-radius: 2px;
  font-size: 10px;
  text-align: center;
  line-height: 1em;
}
</style>
