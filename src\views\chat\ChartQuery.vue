<template>
  <div class="chart-query-page">
    <div class="chat-container">
      <!-- 聊天内容区域 -->
      <div class="chat-content" ref="chatContentRef">
        <div v-if="messages.length === 0" class="chat-welcome">
          <div class="welcome-avatar">
            <img src="@/assets/十一建logo/1.gif" alt="智能助手" class="avatar-gif" />
          </div>
          <div class="welcome-text">
            <h3>Hello,张三</h3>
            <p>我是您的智能表格查询助手，可以帮助您查询公司公开的企业制度、方案规范等文件内的图片或表格</p>
            <p>只需告诉我您的需求，我将竭诚为您服务！</p>
          </div>
        </div>
        
        <!-- 消息列表 -->
        <div v-for="(message, messageIndex) in messages" :key="messageIndex" class="message" :class="message.type">
          <div class="message-avatar">
            <img v-if="message.type === 'assistant'" src="@/assets/十一建logo/1.gif" alt="AI助手" class="avatar-gif" />
            <div v-else class="user-avatar">{{ getCurrentUserAvatar() }}</div>
          </div>
          <div class="message-content">
            <!-- AI消息完成标识 -->
            <div v-if="message.type === 'assistant'" class="ai-status">
              <span class="status-icon">✓</span>
              <span class="status-text">已完成思考</span>
            </div>
            <div class="message-text" v-html="message.content"></div>
            
            <div v-if="message.type === 'assistant'" class="message-actions">
              <button @click="copyMessage(message.content)" class="action-btn" title="复制">
                <el-icon><DocumentCopy /></el-icon>
              </button>
              <button @click="downloadMessage(message.content)" class="action-btn" title="下载">
                <el-icon><Download /></el-icon>
              </button>
           <!--    <button @click="expandMessage(message)" class="action-btn" title="扩写">
                <el-icon><Plus /></el-icon>
              </button>
              <button @click="summarizeMessage(message)" class="action-btn" title="简写">
                <el-icon><Minus /></el-icon>
              </button> -->
              <button @click="regenerateMessage(messageIndex)" class="action-btn" title="重新生成">
                <el-icon><Refresh /></el-icon>
              </button>
           <!--    <button @click="translateMessage(message)" class="action-btn" title="翻译">
                <el-icon><Promotion /></el-icon>
              </button> -->
            </div>
            
            <!-- 显示消息中的文件 -->
            <div v-if="message.files && message.files.length > 0" class="message-files">
              <div v-for="file in message.files" :key="file.id" class="message-file">
                <div class="file-icon">
                  <svg v-if="isImageFile(file)" viewBox="0 0 24 24" width="14" height="14">
                    <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" fill="currentColor"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" width="14" height="14">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                  </svg>
                </div>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
            </div>
            <div class="message-time">{{ message.time }}</div>
          </div>
        </div>
        
        <!-- 打字指示器 -->
        <div v-if="isTyping" class="message assistant">
          <div class="message-avatar">
            <img src="@/assets/十一建logo/1.gif" alt="AI助手" class="avatar-gif" />
          </div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <div class="input-wrapper" 
               @drop="handleDrop" 
               @dragover="handleDragOver" 
               @dragenter="handleDragEnter"
               @dragleave="handleDragLeave"
               :class="{ 'drag-over': isDragOver }"
               style="position: relative;">
            <textarea
              v-model="inputMessage"
              placeholder="您可以输入需要查询的图片或表格资料，比如'查询公司logo'"
              @keyup.enter="sendMessage"
              @paste="handlePaste"
              @input="adjustTextareaHeight"
              ref="textareaRef"
              class="message-input"
              style="resize: none; padding-bottom: 48px; overflow: hidden;"
              rows="1"
            />
            <div class="input-actions">
              <button class="action-btn upload-btn" @click="triggerFileUpload" title="上传文件">
                <svg viewBox="0 0 24 24" width="16" height="16">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                </svg>
              </button>
              <input 
                ref="fileInputRef"
                type="file" 
                multiple
                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.xlsx,.xls,.csv"
                @change="handleFileUpload"
                style="display: none;"
              />
            </div>
            <!-- 框内左下角功能区（Element Plus风格） -->
            <div class="input-advanced input-advanced-inside" style="position: absolute; left: 16px; right: 16px; bottom: 12px; display: flex; align-items: center; justify-content: space-between; z-index: 10;">
              <div style="display: flex; align-items: center;">
                <el-select
                  v-model="searchSource"
                  size="small"
                  class="search-source-select"
                  style="width: 130px;"
                >
                  <el-option
                    v-for="option in searchSourceOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  >
                    <span style="display: flex; align-items: center;">
                      <span style="margin-right: 6px; display: inline-flex; align-items: center;">
                        <component :is="option.icon" style="width: 14px; height: 14px;" />
                      </span>
                      {{ option.label }}
                    </span>
                  </el-option>
                </el-select>
                <el-select
                  v-model="selectedModel"
                  size="small"
                  class="model-select"
                  style="width: 120px; margin-left: 8px;"
                  popper-class="model-select-popper"
                >
                  <el-option
                    v-for="model in modelOptions"
                    :key="model.value"
                    :label="model.label"
                    :value="model.value"
                  />
                </el-select>
              </div>
              <el-button
                class="send-btn"
                type="primary"
                style="width: 36px; height: 36px; border-radius: 8px; padding: 0; display: flex; align-items: center; justify-content: center;"
                @click="isTyping ? stopGeneration() : sendMessage()"
                :disabled="!inputMessage.trim() && uploadedFiles.length === 0 && !isTyping"
                :title="isTyping ? '停止生成' : '发送'"
              >
                <el-icon v-if="isTyping" :size="24">
                  <svg viewBox="0 0 24 24" width="16" height="16">
                    <path d="M6,6H18V18H6V6Z" fill="currentColor"/>
                  </svg>
                </el-icon>
                <el-icon v-else :size="24"><Promotion /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 已上传文件列表 -->
        <div v-if="uploadedFiles.length > 0" class="uploaded-files">
          <div v-for="file in uploadedFiles" :key="file.id" class="file-item">
            <div class="file-icon">
              <svg v-if="isImageFile(file)" viewBox="0 0 24 24" width="16" height="16">
                <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" fill="currentColor"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" width="16" height="16">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
              </svg>
            </div>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
            <button class="remove-file-btn" @click="removeFile(file.id)" title="移除文件">
              <svg viewBox="0 0 24 24" width="14" height="14">
                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- 底部提示 -->
        <div class="input-footer">
          <span class="footer-text">以上内容由大模型生成,请您注意甄别,并自行判断其准确性和适用性.</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from 'vue'
import { ElIcon, ElButton, ElSelect, ElOption, ElMessage } from 'element-plus'
import {
  DocumentCopy,
  Download,
  Plus,
  Minus,
  Refresh,
  Promotion,
  User,
  OfficeBuilding,
  Connection
} from '@element-plus/icons-vue'
import MinimalButton from '@/components/minimal-ui/Button.vue'

const username = 'Admin'
const inputMessage = ref('')
const isTyping = ref(false)
const chatContentRef = ref<HTMLElement>()
const activeTab = ref('enterprise')
const selectedKnowledge = ref<string[]>([])
const fileInputRef = ref<HTMLInputElement>()
const uploadedFiles = ref<UploadedFile[]>([])
const messages = ref<Message[]>([])

// 搜索来源选择
const searchSource = ref('web')
const searchSourceOptions = [
  { value: 'web', label: '联网', icon: Connection },
  { value: 'personal', label: '个人知识库', icon: User },
  { value: 'enterprise', label: '企业知识库', icon: OfficeBuilding },
]

// 计算属性：根据搜索来源确定是否开启联网搜索
const isWebSearch = computed(() => searchSource.value === 'web')

const selectedModel = ref('deepseek')
const currentTypingTimeout = ref<number | null>(null)

const modelOptions = [
  { value: 'gpt-3.5', label: 'GPT-3.5（OpenAI）' },
  { value: 'gpt-4', label: 'GPT-4（OpenAI）' },
  { value: 'wenxin', label: '文心一言（百度）' },
  { value: 'qwen', label: '通义千问（阿里）' },
  { value: 'deepseek', label: 'DeepSeek' },
  { value: 'custom', label: '自定义模型' }
]

const enterpriseOptions = ref<KnowledgeOption[]>([
  { id: 'enterprise-a', name: '企业知识库分类A' },
  { id: 'enterprise-b', name: '企业知识库分类B' },
  { id: 'enterprise-c', name: '企业知识库分类C' },
  { id: 'enterprise-d', name: '企业知识库分类D' }
])

const personalOptions = ref<KnowledgeOption[]>([
  { id: 'personal-a', name: '个人知识库分类A' },
  { id: 'personal-b', name: '个人知识库分类B' },
  { id: 'personal-c', name: '个人知识库分类C' }
])

interface Message {
  type: 'user' | 'assistant'
  content: string
  time: string
  files?: UploadedFile[]
}

interface KnowledgeOption {
  id: string
  name: string
}

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  file: File
}

function triggerFileUpload() {
  fileInputRef.value?.click()
}

function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (files) {
    Array.from(files).forEach(file => {
      addFileToUpload(file)
    })
  }
  
  // 清空文件输入框
  if (target) {
    target.value = ''
  }
}

function removeFile(fileId: string) {
  const index = uploadedFiles.value.findIndex(file => file.id === fileId)
  if (index > -1) {
    uploadedFiles.value.splice(index, 1)
  }
}

function isImageFile(file: UploadedFile): boolean {
  return file.type.startsWith('image/')
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function getCurrentTime() {
  return new Date().toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

function scrollToBottom() {
  nextTick(() => {
    if (chatContentRef.value) {
      chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
    }
  })
}

function askQuestion(question: string) {
  inputMessage.value = question
  sendMessage()
}

function sendMessage() {
  if ((!inputMessage.value.trim() && uploadedFiles.value.length === 0) || isTyping.value) return

  const userMessage: Message = {
    type: 'user',
    content: inputMessage.value || '发送了文件',
    time: getCurrentTime(),
    files: uploadedFiles.value.length > 0 ? [...uploadedFiles.value] : undefined
  }
  
  messages.value.push(userMessage)
  const question = inputMessage.value
  inputMessage.value = ''
  uploadedFiles.value = []
  
  scrollToBottom()
  
  // 模拟AI回复
  isTyping.value = true
  const typingTimeout = setTimeout(() => {
    if (isTyping.value) { // 检查是否被停止
      let responses = ''

      // 根据搜索来源提供不同的回复
      switch (searchSource.value) {
        case 'personal':
          responses = '<p>我已经从您的个人知识库中搜索图表相关信息，正在分析中...</p><p>📊 <strong>分析进度</strong>：正在识别图片内容和表格结构</p><p>基于您个人知识库的内容进行图表分析。</p>'
          break
        case 'enterprise':
          responses = '<p>我已经从企业知识库中搜索图表相关信息，正在分析中...</p><p>📊 <strong>分析进度</strong>：正在识别图片内容和表格结构</p><p>基于企业知识库的内容进行图表分析。</p>'
          break
        case 'web':
          responses = '<p>我已经通过联网搜索获取最新图表信息，正在分析中...</p><p>📊 <strong>分析进度</strong>：正在识别图片内容和表格结构</p><p>基于联网搜索的结果进行图表分析。</p>'
          break
        default:
          responses = '<p>我已经收到您的图表查询请求，正在分析中...</p><p>📊 <strong>分析进度</strong>：正在识别图片内容和表格结构</p><p>这是完整的测试内容，应该能够完全显示。</p>'
      }

      const assistantMessage: Message = {
        type: 'assistant',
        content: responses,
        time: getCurrentTime()
      }

      messages.value.push(assistantMessage)
      isTyping.value = false
      scrollToBottom()
    }
  }, 2000)

  // 保存timeout ID以便停止时清除
  currentTypingTimeout.value = typingTimeout
}

function toggleKnowledge(id: string) {
  const index = selectedKnowledge.value.indexOf(id)
  if (index > -1) {
    selectedKnowledge.value.splice(index, 1)
  } else {
    selectedKnowledge.value.push(id)
  }
}

function removeKnowledge(id: string) {
  const index = selectedKnowledge.value.indexOf(id)
  if (index > -1) {
    selectedKnowledge.value.splice(index, 1)
  }
}

function getKnowledgeName(id: string) {
  const allOptions = [...enterpriseOptions.value, ...personalOptions.value]
  const option = allOptions.find(opt => opt.id === id)
  return option?.name || id
}

// 添加拖拽和粘贴相关状态
const isDragOver = ref(false)
const inputRef = ref<HTMLInputElement>()

// 处理粘贴事件
function handlePaste(event: ClipboardEvent) {
  const items = event.clipboardData?.items
  if (!items) return
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    
    // 处理图片粘贴
    if (item.type.indexOf('image') !== -1) {
      event.preventDefault()
      const file = item.getAsFile()
      if (file) {
        addFileToUpload(file)
        inputMessage.value = inputMessage.value + `[已粘贴图片: ${file.name || 'image.png'}] `
      }
    }
    
    // 处理文本粘贴（可能包含表格数据）
    if (item.type === 'text/plain') {
      item.getAsString((text) => {
        // 检测是否为表格数据（制表符分隔或逗号分隔）
        if (isTableData(text)) {
          createTableFile(text)
        }
      })
    }
    
    // 处理HTML粘贴（从Excel等复制的表格）
    if (item.type === 'text/html') {
      item.getAsString((html) => {
        const tableData = extractTableFromHTML(html)
        if (tableData) {
          createTableFile(tableData)
        }
      })
    }
  }
}

// 处理拖拽进入
function handleDragEnter(event: DragEvent) {
  event.preventDefault()
  isDragOver.value = true
}

// 处理拖拽悬停
function handleDragOver(event: DragEvent) {
  event.preventDefault()
}

// 处理拖拽离开
function handleDragLeave(event: DragEvent) {
  event.preventDefault()
  // 检查是否真的离开了拖拽区域
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY
  
  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false
  }
}

// 处理文件拖拽放置
function handleDrop(event: DragEvent) {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (files) {
    Array.from(files).forEach(file => {
      addFileToUpload(file)
    })
    
    if (files.length > 0) {
      inputMessage.value = inputMessage.value + `[已拖拽 ${files.length} 个文件] `
    }
  }
}

// 添加文件到上传列表
function addFileToUpload(file: File) {
  const uploadedFile: UploadedFile = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    name: file.name,
    size: file.size,
    type: file.type,
    file: file
  }
  uploadedFiles.value.push(uploadedFile)
}

// 检测是否为表格数据
function isTableData(text: string): boolean {
  const lines = text.split('\n').filter(line => line.trim())
  if (lines.length < 2) return false
  
  // 检查是否有制表符或多个逗号分隔
  const hasTabSeparator = lines.some(line => line.includes('\t'))
  const hasCommaSeparator = lines.some(line => (line.match(/,/g) || []).length >= 2)
  
  return hasTabSeparator || hasCommaSeparator
}

// 从HTML中提取表格数据
function extractTableFromHTML(html: string): string | null {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  const table = doc.querySelector('table')
  
  if (!table) return null
  
  const rows = table.querySelectorAll('tr')
  const tableData: string[] = []
  
  rows.forEach(row => {
    const cells = row.querySelectorAll('td, th')
    const rowData = Array.from(cells).map(cell => cell.textContent?.trim() || '')
    tableData.push(rowData.join('\t'))
  })
  
  return tableData.join('\n')
}

// 创建表格文件
function createTableFile(tableData: string) {
  const blob = new Blob([tableData], { type: 'text/plain' })
  const file = new File([blob], `table_${Date.now()}.txt`, { type: 'text/plain' })
  addFileToUpload(file)
  inputMessage.value = inputMessage.value + `[已粘贴表格数据] `
}

const textareaRef = ref<HTMLTextAreaElement>()

// 停止生成功能
const stopGeneration = () => {
  if (isTyping.value && currentTypingTimeout.value) {
    clearTimeout(currentTypingTimeout.value)
    isTyping.value = false
    currentTypingTimeout.value = null
    // 可以添加消息提示
    console.log('已停止生成')
  }
}

function adjustTextareaHeight() {
  if (textareaRef.value) {
    textareaRef.value.style.height = 'auto'
    textareaRef.value.style.height = textareaRef.value.scrollHeight + 'px'
  }
}

// 获取当前用户头像文字
function getCurrentUserAvatar(): string {
  const currentUsername = localStorage.getItem('currentUsername')
  if (currentUsername) {
    return currentUsername.charAt(0).toUpperCase()
  }
  
  const userRole = localStorage.getItem('userRole') || 'user'
  switch (userRole) {
    case 'admin':
      return '管'
    case 'staff':
      return '员'
    default:
      return '用'
  }
}

// 复制消息
function copyMessage(content: string) {
  const textContent = content.replace(/<[^>]*>/g, '') // 移除HTML标签
  navigator.clipboard.writeText(textContent).then(() => {
    ElMessage.success('复制成功')
  })
}

// 下载消息
function downloadMessage(content: string) {
  const textContent = content.replace(/<[^>]*>/g, '')
  const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `message_${Date.now()}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// 扩写消息
function expandMessage(message: Message) {
  inputMessage.value = `请扩写以下内容：\n${message.content.replace(/<[^>]*>/g, '')}`
}

// 简写消息
function summarizeMessage(message: Message) {
  inputMessage.value = `请简写以下内容：\n${message.content.replace(/<[^>]*>/g, '')}`
}

// 重新生成消息
const regenerateMessage = (messageIndex: number) => {
  console.log('重新生成被点击了，索引:', messageIndex)
  
  if (messageIndex > 0) {
    const previousMessage = messages.value[messageIndex - 1]
    if (previousMessage.type === 'user') {
      console.log('找到用户消息，开始重新生成')
      
      // 移除当前AI消息
      messages.value.splice(messageIndex, 1)
      
      // 模拟重新生成AI回复
      isTyping.value = true
      
      const typingTimeout = setTimeout(() => {
        if (isTyping.value) {
          const responses = [
            '<p>重新生成的回答：我已经收到您的图表查询请求，正在重新分析中...</p>',
            '<p>基于您的问题，我重新为您整理了以下信息...</p>',
            '<p>重新分析后，为您提供更准确的回答...</p>'
          ]

          const newAssistantMessage = {
            type: 'assistant',
            content: responses[Math.floor(Math.random() * responses.length)],
            time: getCurrentTime()
          }

          messages.value.push(newAssistantMessage)
          isTyping.value = false
          scrollToBottom()
        }
      }, 2000)

      currentTypingTimeout.value = typingTimeout
      scrollToBottom()
    }
  }
}

// 翻译消息
function translateMessage(message: Message) {
  inputMessage.value = `请翻译以下内容：\n${message.content.replace(/<[^>]*>/g, '')}`
}
</script>

<style scoped>
.chat-header {
  padding: 1rem 3rem 1.5rem 3rem;
  background: var(--minimal-bg);
  border-bottom: 1px solid rgba(91, 124, 255, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
}
.title-bar {
  width: 4px;
  height: 20px;
  background: var(--minimal-primary-gradient);
  border-radius: 2px;
  margin-right: 12px;
}

.chart-query-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-container {
  width: 100%;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}

.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  padding: 60px 20px;
  min-height: 400px;
}

.welcome-avatar {
  margin-bottom: 30px;
}

.avatar-gif {
  width: 120px;
  height: 120px;

  object-fit: cover;
  border-radius: 24px;
opacity: 1;
  box-shadow: 0px 0px 20px 0px rgba(164, 133, 255, 0.5);
}

.welcome-text h3 {
  font-size: 24px;
  font-weight: 500;
  color: #323742;
  margin: 0 0 20px 0;
  font-family: Source Han Sans;
}

.welcome-text p {
  letter-spacing: 0px;
  font-variation-settings: "opsz" auto;
font-feature-settings: "kern" on;
  font-size: 16px;
  color: #323742;
  line-height: 1.6;
  margin: 0 0 10px 0;
  line-height: 32px;
  font-family: Source Han Sans;
}

.sub-text {
  font-size: 14px !important;
  color: #999 !important;
}

.message {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
  width: 100%;
}

.message.user {
  flex-direction: row-reverse;
  /* 确保用户消息从右侧开始排列 */
}

.message.assistant {
  /* AI消息从左侧开始排列 */
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 12px;
  flex-shrink: 0;
}

.message-avatar .avatar-gif {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar {
  background: var(--minimal-primary-gradient);
  color: white;
  font-weight: 600;
  font-size: 14px;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-content {
  position: relative;
  display: flex;
  flex-direction: column;
  max-height: none;
  overflow: visible;
  /* 自适应宽度，但不设置flex: 1 */
  width: fit-content;
  max-width: 80%;
  min-width: 60px;
}

/* 用户消息内容右对齐 */
.message.user .message-content {
  align-items: flex-end;
}

/* AI消息内容左对齐 */
.message.assistant .message-content {
  align-items: flex-start;
}

.message-text {
  padding: 16px 20px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.6;
  word-wrap: break-word;
  margin-bottom: 6px;
  width: fit-content;
  min-width: 40px;
}

/* 用户消息样式 */
.message.user .message-text {
  background: var(--minimal-primary-gradient);
  color: white;
  border-bottom-right-radius: 6px;
}

/* AI消息样式 */
.message.assistant .message-text {
  background: #f1f3f5;
  color: #333;
  border-bottom-left-radius: 6px;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 6px;
  padding: 0 4px;
  opacity: 0.8;
}

.message.user .message-time {
  text-align: right;
  color: #999;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-6px);
    opacity: 1;
  }
}

.chat-input {
width: 100%;
  padding: 16px 0px;
  margin: auto;
}

.input-container {
  position: relative;
}

.input-wrapper {
 display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 18px 20px;
  transition: all 0.2s ease;
  min-height: 100px;
  max-height: 600px;
  font-size: 16px;
  position: relative;
  flex-wrap: wrap;
  overflow-y: auto;
}

.input-wrapper:hover {
  border-color: #40a9ff;
}

.input-wrapper:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.input-wrapper.drag-over {
  background: rgba(24, 144, 255, 0.05);
  border-color: #1890ff;
  border-style: dashed;
}
:deep(.el-select--small .el-select__wrapper){
  width:100%;
}

.message-input {
flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  padding: 0;
  color: #333;
  line-height: 1.5;
  resize: none;
  min-height: 40px;
  max-height: 520px;
  overflow-y: auto;
}

.message-input::placeholder {
  color: #bfbfbf;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 16px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f5f5f5;
  color: #666;
}

.action-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.upload-btn {
  background: transparent;
  color: #999;
}

.upload-btn:hover {
  background: #f0f0f0;
  color: #666;
}

.file-size-selector {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.size-text {
  white-space: nowrap;
}

.size-select {
  border: none;
  background: transparent;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  outline: none;
  padding: 2px 4px;
}

.size-select:hover {
  background: #f0f0f0;
  border-radius: 4px;
}

.send-btn {
  background:var(--minimal-primary-gradient);
  color: white;
  border-radius: 6px;
  margin-left: auto;
}

.send-btn:hover:not(:disabled) {
  background:var(--minimal-primary-gradient);
}

.send-btn:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

.uploaded-files {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 12px;
  color: #666;
  gap: 6px;
}

.file-icon {
  color: #1890ff;
}

.file-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: #999;
  font-size: 11px;
}

.remove-file-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.remove-file-btn:hover {
  background: #f5f5f5;
  color: #ff4d4f;
}

.input-footer {
  margin-top: 12px;
  text-align: center;
}

.footer-text {
  font-size: 12px;
  color: #999;
}

.message-files {
  margin-top: 8px;
}

.message-file {
  display: flex;
  align-items: center;
  background: #fafafa;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 12px;
  color: #666;
  gap: 6px;
  margin-bottom: 4px;
}

.message.user .message-file {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.input-advanced {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-top: 8px;
  margin-bottom: 4px;
  font-size: 14px;
  color: #666;
}
.web-search-switch {
  display: flex;
  align-items: center;
  gap: 6px;
  user-select: none;
}
.model-select {
  display: flex;
  align-items: center;
  gap: 6px;
}
.model-select select {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 14px;
  outline: none;
  transition: border 0.2s;
}
.model-select select:focus {
  border-color: #1890ff;
}
/* 框内左下角功能区 */
.input-advanced-inside {
  position: absolute;
  left: 16px;
  bottom: 10px;
  display: flex;
  align-items: center;
  gap: 18px;
  font-size: 13px;
  color: #888;
  z-index: 2;
  background: transparent;
  pointer-events: auto;
}
.input-advanced-inside .web-search-btn {
  /* background: var(--minimal-primary-gradient); */
  min-width: 88px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 4px;
  transition: background 0.2s, color 0.2s;
  box-shadow: none;
  border: 1px solid #e0e0e0;
  margin-right: 0;
  padding: 0 12px;
}
/* 移除下方这段会覆盖主色的规则 */
/* :deep(.input-advanced-inside .el-button.is-primary) {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
  box-shadow: none;
} */
:deep(.input-advanced-inside .el-button.is-primary:focus),
:deep(.input-advanced-inside .el-button.is-primary:active) {
  border-color: #1890ff !important;
  box-shadow: none !important;
}
.input-advanced-inside .model-select {
  min-width: 100px;
}
.model-select-popper {
  font-size: 13px;
}
.input-advanced-inside .switch-label {
  margin-left: 2px;
}
.input-advanced-inside .model-select {
  display: flex;
  align-items: center;
  gap: 4px;
}
.input-advanced-inside select {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 13px;
  outline: none;
  background: #fafbfc;
  color: #333;
  transition: border 0.2s;
}
.input-advanced-inside select:focus {
  border-color: #1890ff;
}
@media (max-width: 600px) {
  .input-advanced-inside {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    left: 8px;
    bottom: 8px;
  }
}
.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  opacity: 1;
}

.message-actions .action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.message-actions .action-btn svg {
  width: 14px;
  height: 14px;
  fill: currentColor;
}

.message-actions .action-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--el-color-primary);
}
</style>

<!-- 新增全局样式，确保渐变主色全局覆盖 -->
<style>
/* 最高优先级的样式覆盖 */
:deep(.el-button--primary) {
  background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  background-image: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: none !important;
}

:deep(.el-button--primary:hover),
:deep(.el-button--primary:focus),
:deep(.el-button--primary:active) {
  background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  background-image: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: none !important;
}

/* 专门针对联网搜索按钮 */
.web-search-btn.el-button--primary {
  background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  background-image: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: none !important;
}

.web-search-btn.el-button--primary:hover,
.web-search-btn.el-button--primary:focus,
.web-search-btn.el-button--primary:active {
  background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  background-image: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: none !important;
}

/* 输入框内的按钮 */
:deep(.input-advanced-inside .el-button--primary) {
  background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  background-image: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: none !important;
}

:deep(.input-advanced-inside .el-button--primary:hover),
:deep(.input-advanced-inside .el-button--primary:focus),
:deep(.input-advanced-inside .el-button--primary:active) {
  background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  background-image: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: none !important;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(91, 124, 255, 0.08);
  color: #5b7cff;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  margin-bottom: 12px;
  width: fit-content;
  border: 1px solid rgba(91, 124, 255, 0.15);
}

.status-icon {
  font-size: 10px;
  font-weight: bold;
}

.status-text {
  font-weight: 500;
}

/* 添加消息样式类 */
.message-bold {
  font-weight: 600;
  color: var(--el-color-primary);
}

.message-list-item {
  display: flex;
  align-items: flex-start;
  margin: 8px 0;
  line-height: 1.6;
}

.message-unordered-item .message-list-bullet {
  color: var(--el-color-primary);
  font-weight: bold;
  margin-right: 8px;
  min-width: 16px;
}

.message-list-content {
  flex: 1;
}

.message-quote {
  border-left: 4px solid var(--el-color-primary);
  padding: 12px 16px;
  margin: 12px 0;
  background: var(--el-color-primary-light-9);
  border-radius: 0 8px 8px 0;
  font-style: italic;
}

/* 搜索来源下拉框样式 */
.search-source-select {
  border-radius: 6px;
  font-size: 12px;
}

.search-source-select .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  border-radius: 6px;
}

.search-source-select .el-input__suffix {
  height: 28px;
  line-height: 28px;
}

.search-source-select .el-select__caret {
  line-height: 28px;
}
</style>










































