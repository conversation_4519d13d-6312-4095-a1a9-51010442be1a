<template>
  <el-dialog
    v-model="visible"
    title=""
    width="90%"
    class="preview-dialog"
    :before-close="handleClose"
    top="5vh"
  >
    <template #header>
      <div class="preview-dialog-header">
        <div class="preview-title">
          <div class="title-bar"></div>
          <span>预览 - {{ currentPreviewFile?.name || '' }}</span>
        </div>
      </div>
    </template>
    <div class="preview-content">
      <!-- 文件信息头部 -->
 <!--      <div class="preview-file-header">
        <div class="file-title">{{ currentPreviewFile?.name }}</div>
        <div class="file-status">
          <el-tag type="success" size="small">{{ currentPreviewFile?.category }}</el-tag>
        </div>
      </div> -->

      <!-- 文档预览区域 -->
      <div class="document-viewer">
        <div class="document-content">
          <div class="content-section">
            <div class="section-title">第一章 安全管理总则</div>
            <div class="section-content">
              <p>1.1 为了加强企业安全生产管理，保障员工生命安全和身体健康...</p>
              <p>1.2 本制度适用于公司所有部门和全体员工...</p>
              <p>1.3 安全生产工作坚持"安全第一、预防为主、综合治理"的方针...</p>
            </div>
          </div>

          <div class="content-section">
            <div class="section-title">第二章 安全管理组织机构</div>
            <div class="section-content">
              <p>2.1 公司设立安全生产委员会，负责安全生产工作的统一领导...</p>
              <p>2.2 各部门设立安全员，负责本部门的安全管理工作...</p>
            </div>
          </div>

          <div class="content-section">
            <div class="section-title">第三章 安全管理制度</div>
            <div class="section-content">
              <p>3.1 建立健全安全生产责任制，明确各级人员安全职责...</p>
              <p>3.2 定期开展安全检查，及时发现和消除安全隐患...</p>
              <p>3.3 建立安全培训制度，提高员工安全意识和技能...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

<!--       <template #footer>
        <div class="preview-actions">
          <el-button @click="handlePreviewClose">关闭</el-button>
        </div>
      </template> -->
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface PreviewFile {
  name?: string
  type?: string
  url?: string
  content?: string
  size?: string
  category?: string
}

interface Props {
  visible: boolean
  currentPreviewFile?: PreviewFile | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const currentPreviewFile = computed(() => props.currentPreviewFile)

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
/* 预览弹窗样式 */
:deep(.preview-dialog .el-dialog) {
  border-radius: 8px;
  overflow: hidden;
  max-height: 90vh;
}

:deep(.preview-dialog .el-dialog__header) {
  padding: 0;
  margin-bottom: 0;
  border-bottom: 1px solid #f0f0f0;
  background: #ffffff;
}

.preview-dialog-header {
  padding: 20px 24px;
  background: #ffffff;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.preview-title .title-bar {
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #5b7cff 0%, #a685ff 100%);
  border-radius: 2px;
}

:deep(.preview-dialog .el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 120px);
  overflow: hidden;
}

:deep(.preview-dialog .el-dialog__footer) {
  padding: 0;
  border-top: 1px solid #e5e7eb;
}

.preview-content {
  display: flex;
  flex-direction: column;
  height: calc(90vh - 120px);
}

.preview-file-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #f8f9ff;
  border-bottom: 1px solid #e5e7eb;
}

.file-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.file-status {
  display: flex;
  align-items: center;
}

.document-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.viewer-header {
  padding: 20px 24px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  background: white;
}

.viewer-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.viewer-subtitle {
  font-size: 14px;
  color: #666;
}

.document-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: white;
}

.content-section {
  margin-bottom: 32px;
}

.content-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #5b7cff;
  display: inline-block;
}

.section-content {
  line-height: 1.8;
}

.section-content p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #555;
  text-indent: 2em;
}

.section-content p:last-child {
  margin-bottom: 0;
}

.preview-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  background: #f8f9ff;
}
</style>
