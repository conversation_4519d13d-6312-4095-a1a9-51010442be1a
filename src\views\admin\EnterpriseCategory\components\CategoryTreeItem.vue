<template>
  <div class="tree-item" :class="{ 'expanded': item.expanded }">
    <!-- 当前项的行 -->
    <div class="tree-row">
      <div class="category-col">
        <div class="category-content" :style="{ paddingLeft: (level * 20) + 'px' }">
          <el-button
            v-if="item.children && item.children.length > 0"
            :icon="item.expanded ? ArrowDown : ArrowRight"
            @click="$emit('toggle', item.id)"
            size="small"
            text
            class="expand-btn"
          />
          <span v-else class="expand-placeholder"></span>
          <span class="category-name" :class="{ [`level-${level}`]: true }">{{ item.name }}</span>
        </div>
      </div>
      <div class="status-col">
        <el-tag :type="getStatusTagType(item.isEnabled)" size="small">
          {{ item.isEnabled ? '是' : '否' }}
        </el-tag>
      </div>
      <div class="date-col">
        <span>{{ item.createTime }}</span>
      </div>
      <div class="action-col">
        <div class="action-buttons">
          <el-button 
            type="primary" 
            size="small" 
            plain 
            @click="$emit('edit', item)"
            style="color: #a685ff; border-color: #a685ff; background-color: #f3f0ff;"
          >
            修改
          </el-button>
          <el-button 
            type="primary" 
            size="small" 
            plain 
            @click="$emit('add', item)"
          >
            新增
          </el-button>
          <el-button 
            v-if="level > 0"
            type="danger" 
            size="small" 
            plain 
            @click="$emit('delete', item)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 递归渲染子项 -->
    <div v-if="item.expanded && item.children && item.children.length > 0" class="tree-children">
      <CategoryTreeItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
        :level="level + 1"
        @toggle="$emit('toggle', $event)"
        @edit="$emit('edit', $event)"
        @add="$emit('add', $event)"
        @delete="$emit('delete', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue'

interface CategoryItem {
  id: string
  name: string
  isEnabled: boolean
  createTime: string
  expanded?: boolean
  children?: CategoryItem[]
}

interface Props {
  item: CategoryItem
  level: number
}

defineProps<Props>()

defineEmits<{
  toggle: [id: string]
  edit: [item: CategoryItem]
  add: [item: CategoryItem]
  delete: [item: CategoryItem]
}>()

// 根据状态返回标签类型
const getStatusTagType = (isEnabled: boolean) => {
  return isEnabled ? 'primary' : 'danger'
}
</script>

<style scoped>

.tree-row {
  display: flex;
  align-items: center;
  min-height: 48px;
  transition: background-color 0.2s;
}

.tree-row:hover {
  background-color: #f8fafc;
}

.category-col {
  flex: 1;
  display: flex;
  align-items: center;
}

.category-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  min-width: 24px;
}

.expand-placeholder {
  width: 24px;
  height: 24px;
  display: inline-block;
}

.category-name {
  font-weight: 500;
  color: #1f2937;
}

.category-name.level-0 {
  font-weight: 600;
}

.status-col {
  width: 100px;
  display: flex;
  justify-content: center;
}

.date-col {
  width: 180px;
  display: flex;
  justify-content: center;
  color: #6b7280;
  font-size: 14px;
}

.action-col {
  width: 200px;
  display: flex;
  justify-content: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}


</style>
