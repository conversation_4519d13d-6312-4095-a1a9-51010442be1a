<template>
  <div class="chat-page">
    <!-- 上方内容区域 -->
    <div class="chat-main">
      <!-- 左侧知识库选择区域 -->
      <div class="chat-left" v-show="searchSources.includes('personal') || searchSources.includes('enterprise')">
        <div class="knowledge-selector">
          <!-- 知识库分类标题 -->
          <div class="knowledge-title">
            <div class="title-line"></div>
            <span>知识库分类</span>
          </div>
        
          
          <!-- 知识库类型Tab页 -->
          <div v-if="searchSources.includes('personal') || searchSources.includes('enterprise')" class="knowledge-tabs">
            <template v-if="searchSources.includes('enterprise') && searchSources.includes('personal')">
              <!-- 同时选择了企业和个人知识库，显示Tab页 -->
              <div class="tab-buttons">
                <button
                  class="tab-button"
                  :class="{ active: activeKnowledgeTab === 'enterprise' }"
                  @click="activeKnowledgeTab = 'enterprise'"
                >
                  企业知识库
                  <span v-if="selectedEnterpriseKnowledge.length > 0" class="tab-badge">{{ selectedEnterpriseKnowledge.length }}</span>
                </button>
                <button
                  class="tab-button"
                  :class="{ active: activeKnowledgeTab === 'personal' }"
                  @click="activeKnowledgeTab = 'personal'"
                >
                  个人知识库
                  <span v-if="selectedPersonalKnowledge.length > 0" class="tab-badge">{{ selectedPersonalKnowledge.length }}</span>
                </button>
              </div>
            </template>
            <template v-else>
              <!-- 只选择了一种知识库，显示类型指示器 -->
              <div class="knowledge-type-indicator">
                <span class="type-label">
                  {{ searchSources.includes('enterprise') ? '企业知识库' : '个人知识库' }}
                </span>
              </div>
            </template>
          </div>
          
          <!-- 搜索框 -->
          <div class="knowledge-search">
            <input
              type="text"
              placeholder="输入分类搜索"
              class="knowledge-search-input"
              v-model="searchKeyword"
              @input="handleSearch"
              @keyup.enter="handleSearch"
            >
            <button v-if="!searchKeyword" class="knowledge-search-btn" @click="handleSearch">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
              搜索
            </button>
            <button v-else class="knowledge-search-btn clear-btn" @click="clearSearch">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
              清除
            </button>
          </div>
           <!-- 选择操作按钮 -->
          <div class="selection-controls">
            <div class="select-all-wrapper">
              <div 
                class="select-all-checkbox"
                :class="{ checked: isAllSelected }"
                @click="toggleSelectAll"
              >
                <el-icon v-if="isAllSelected">
                  <Check />
                </el-icon>
              </div>
              <span class="select-all-text" @click="toggleSelectAll">全选</span>
            </div>
            
            <span class="selected-count">
              <template v-if="searchSources.includes('enterprise') && searchSources.includes('personal')">
                已选 {{ selectedKnowledge.length }} 项 (总计: {{ selectedEnterpriseKnowledge.length + selectedPersonalKnowledge.length }} 项)
              </template>
              <template v-else>
                已选 {{ selectedKnowledge.length }} 项
              </template>
            </span>
            
            <span 
              class="clear-selection" 
              @click="clearSelection"
              :class="{ disabled: selectedKnowledge.length === 0 }"
            >
              清除选择
            </span>
          </div>
          
          <!-- 知识库分类列表 -->
          <div class="knowledge-categories">
            <div
              v-for="category in knowledgeCategories"
              :key="category.id"
              class="knowledge-category"
            >
              <div class="category-title" @click="toggleCategory(category.id)">
                <span class="category-icon">{{ category.icon }}</span>
                <span>{{ category.name }}</span>
                <el-icon v-if="category.children && category.children.length > 0" class="expand-icon" :class="{ expanded: expandedCategories.includes(category.id) }">
                  <ArrowRight />
                </el-icon>
              </div>

              <!-- 子分类列表 - 无限层级递归 -->
              <div v-if="expandedCategories.includes(category.id) && category.children" class="category-list">
                <TreeNode
                  v-for="item in category.children"
                  :key="item.id"
                  :node="item"
                  :level="1"
                  :selected-knowledge="selectedKnowledge"
                  :expanded-items="expandedItems"
                  @toggle-knowledge="toggleKnowledge"
                  @toggle-expand="handleToggleExpand"
                  @toggle-parent="toggleParentSelection"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧聊天区域 -->
      <div class="chat-right" :class="{ 'full-width': !searchSources.includes('personal') && !searchSources.includes('enterprise') }">
        <div class="chat-content" ref="chatContentRef">
          <div v-if="messages.length === 0" class="chat-welcome">
            <div class="welcome-avatar">
              <img src="@/assets/十一建logo/1.gif" alt="智能助手" class="avatar-gif" />
            </div>
            <div class="welcome-text">
              <h3>Hello,张三</h3>
              <p>我可以回答各种问题，请把你的问题交给我！</p>
            </div>
            
            <div class="quick-questions">
              <div class="question-row">
                <button class="quick-question" @click="askQuestion('1.企业办了哪些证001')">
                  1.企业办了哪些证001
                </button>
                <button class="quick-question" @click="askQuestion('2.企业办了哪些证002')">
                  2.企业办了哪些证002
                </button>
              </div>
              <div class="question-row">
                <button class="quick-question" @click="askQuestion('3.企业办了哪些证003')">
                  3.企业办了哪些证003
                </button>
                <button class="quick-question" @click="askQuestion('4.企业办了哪些证004')">
                  4.企业办了哪些证004
                </button>
              </div>
            </div>
          </div>
          
          <!-- 消息列表 -->
          <div v-for="message in messages" :key="message.time" class="message" :class="message.type">
            <div class="message-avatar">
              <img v-if="message.type === 'assistant'" src="@/assets/十一建logo/1.gif" alt="AI助手" class="avatar-gif" />
              <div v-else class="user-avatar">{{ getCurrentUserAvatar() }}</div>
            </div>
            <div class="message-content">
              <!-- AI消息完成标识 -->
              <div v-if="message.type === 'assistant'" class="ai-status">
                <span class="status-icon">✓</span>
                <span class="status-text">已完成思考</span>
              </div>
              <!-- 翻译状态指示器 -->
              <div v-if="message.isTranslated" class="translation-indicator">
                <svg viewBox="0 0 24 24" width="14" height="14" fill="#4285f4">
                  <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
                </svg>
                <span>已翻译</span>
              </div>
              <div class="message-text" v-html="message.content"></div>

              <!-- 显示消息中的文件 -->
              <div v-if="message.files && message.files.length > 0" class="message-files">
                <div v-for="file in message.files" :key="file.id" class="message-file-tab">
                  <div class="message-file-icon">
                    <svg viewBox="0 0 24 24" width="16" height="16" fill="#4285f4">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                  </div>
                  <div class="message-file-content">
                    <div class="message-file-title">{{ getFileDisplayName(file.name) }}</div>
                    <div class="message-file-url">{{ file.name }}</div>
                  </div>
                  <div class="message-file-size">{{ formatFileSize(file.size) }}</div>
                </div>
              </div>

              <!-- 消息操作按钮 -->
              <div v-if="message.type === 'assistant'" class="message-actions">
                <button @click="copyMessage(message.content)" class="action-btn" title="复制">
                  <svg viewBox="0 0 24 24" width="14" height="14">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                  </svg>
                </button>
                <button @click="downloadMessage(message.content)" class="action-btn" title="下载">
                  <svg viewBox="0 0 24 24" width="14" height="14">
                    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill="currentColor"/>
                  </svg>
                </button>
                <button @click="expandMessage(message)" class="action-btn" title="扩写">
                  <svg viewBox="0 0 24 24" width="14" height="14">
                    <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="currentColor"/>
                  </svg>
                </button>
                <button @click="summarizeMessage(message)" class="action-btn" title="简写">
                  <svg viewBox="0 0 24 24" width="14" height="14">
                    <path d="M19,13H5V11H19V13Z" fill="currentColor"/>
                  </svg>
                </button>
                <button @click="regenerateMessage(message)" class="action-btn" title="重新生成">
                  <svg viewBox="0 0 24 24" width="14" height="14">
                    <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" fill="currentColor"/>
                  </svg>
                </button>
                <button @click="toggleTranslation(message)"
                        class="action-btn"
                        :class="{ 'translated': message.isTranslated }"
                        :title="message.isTranslated ? '返回原文' : '翻译'">
                  <svg v-if="!message.isTranslated" viewBox="0 0 24 24" width="14" height="14">
                    <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z" fill="currentColor"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" width="14" height="14">
                    <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="currentColor"/>
                  </svg>
                </button>
                <button @click="deleteMessage(message)" class="action-btn delete-btn" title="删除">
                  <svg viewBox="0 0 24 24" width="14" height="14">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="currentColor"/>
                  </svg>
                </button>
              </div>

              <!-- 搜索来源 -->
              <div v-if="message.type === 'assistant' && message.searchSources && message.searchSources.length > 0" class="search-sources">
                <div class="sources-header">
                  <svg viewBox="0 0 24 24" width="16" height="16" fill="#5b7cff">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                  </svg>
                  <span class="sources-title">基于以下搜索来源</span>
                </div>
                <div class="sources-grid">
                  <div v-for="(source, index) in message.searchSources" :key="index"
                       class="source-card"
                       :class="getSourceCardClass(source.category)">
                    <div class="source-header">
                      <div class="source-title">{{ source.title }}</div>
                      <div v-if="source.category"
                           class="source-category"
                           :class="getSourceCategoryClass(source.category)">
                        {{ source.category }}
                      </div>
                    </div>
                    <div class="source-content">{{ source.content }}</div>
                    <div v-if="source.url" class="source-url">{{ source.url }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 打字指示器 -->
          <div v-if="isTyping" class="message assistant">
            <div class="message-avatar">
              <img src="@/assets/十一建logo/1.gif" alt="AI助手" class="avatar-gif" />
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部输入框区域 -->
    <div class="chat-input">
      <div class="input-container">
        <!-- 已上传文件列表 - 显示在输入框上方 -->
        <div v-if="uploadedFiles.length > 0" class="uploaded-files-inline">
          <div v-for="file in uploadedFiles" :key="file.id" class="file-tab">
            <div class="file-tab-icon">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="#4285f4">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </div>
            <div class="file-tab-content">
              <div class="file-tab-title">{{ getFileDisplayName(file.name) }}</div>
              <div class="file-tab-url">{{ file.name }}</div>
            </div>
            <button class="file-tab-close" @click="removeFile(file.id)" title="移除文件">
              <svg viewBox="0 0 24 24" width="12" height="12">
                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="input-wrapper"
             @drop="handleDrop"
             @dragover="handleDragOver"
             @dragenter="handleDragEnter"
             @dragleave="handleDragLeave"
             :class="{ 'drag-over': isDragOver }">
          <textarea
            v-model="inputMessage"
            placeholder="请输入问题，Enter发送，Shift+Enter换行"
            @keyup.enter="sendMessage"
            @input="adjustTextareaHeight"
            ref="textareaRef"
            class="message-input"
            rows="1"
          />
          
          <div class="input-actions">
            <div class="left-actions">
              <el-select
                v-model="searchSources"
                multiple
                size="small"
                class="search-source-select"
                style="width: 160px;"
                collapse-tags
                collapse-tags-tooltip
                placeholder="选择搜索来源"
              >
                <el-option
                  v-for="option in searchSourceOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                >
                  <span style="display: flex; align-items: center;">
                    <span style="margin-right: 6px; display: inline-flex; align-items: center;">
                      <component :is="option.icon" style="width: 14px; height: 14px;" />
                    </span>
                    {{ option.label }}
                  </span>
                </el-option>
              </el-select>
              <el-select
                v-model="selectedModel"
                size="small"
                class="model-select"
                style="width: 120px; margin-left: 8px;"
              >
                <el-option
                  v-for="model in modelOptions"
                  :key="model.value"
                  :label="model.label"
                  :value="model.value"
                />
              </el-select>
            </div>
            <div class="right-actions">
              <!-- 隐藏的文件输入元素 -->
              <input
                ref="fileInputRef"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.xls,.xlsx,.csv"
                @change="handleFileUpload"
                style="display: none;"
              />
              <el-button
                class="upload-btn"
                size="small"
                @click="triggerFileUpload"
                title="上传文件"
                style="width: 36px; height: 36px;"
              >
                <img src="@/assets/icon/file.svg" alt="上传文件" style="width: 36px; height: 36px; border-radius: 8px; padding: 0; display: flex; align-items: center; justify-content: center;" />
              </el-button>
              <el-button
                class="send-btn"
                type="primary"
                 style="width: 36px; height: 36px; border-radius: 8px; padding: 0; display: flex; align-items: center; justify-content: center;"
                @click="isTyping ? stopGeneration() : sendMessage()"
                :disabled="isSendDisabled"
                :title="isTyping ? '停止生成' : '发送'"
              >
                <el-icon v-if="isTyping" :size="24">
                  <svg viewBox="0 0 24 24" width="24" height="24">
                      <path d="M6,6H18V18H6V6Z" fill="currentColor"/>
                    </svg>
                </el-icon>
                <el-icon v-else :size="24"><Promotion /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 知识库选择提示 -->
      <div v-if="(searchSources.includes('personal') || searchSources.includes('enterprise')) && !isKnowledgeSelectionComplete" class="knowledge-tip">
        <el-alert
          :title="getKnowledgeSelectionTip()"
          type="warning"
          :closable="false"
          show-icon
          style="margin-bottom: 8px;"
        />
      </div>

      <div class="input-footer">
        <span class="footer-text">以上内容由AI大模型生成，请甄别后使用，请勿输入个人隐私信息</span>
      </div>
    </div>
  </div>
</template>





<script setup lang="ts">
import { ref, nextTick, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElIcon, ElButton, ElSelect, ElOption, ElMessage } from 'element-plus'
import { Promotion, Check, ArrowRight, User, OfficeBuilding, Connection } from '@element-plus/icons-vue'
import TreeNode from '@/components/TreeNode.vue'

const username = 'Admin'
const inputMessage = ref('')
const isTyping = ref(false)
const chatContentRef = ref<HTMLElement>()
// 分别维护企业知识库和个人知识库的选择状态
const selectedEnterpriseKnowledge = ref<string[]>([])
const selectedPersonalKnowledge = ref<string[]>([])
const fileInputRef = ref<HTMLInputElement>()
const uploadedFiles = ref<UploadedFile[]>([])
const messages = ref<Message[]>([])

// 搜索来源选择（多选）
const searchSources = ref(['enterprise'])

// 当前激活的知识库Tab页
const activeKnowledgeTab = ref('enterprise')

// 搜索关键词
const searchKeyword = ref('')

// 过滤后的知识库分类
const filteredKnowledgeCategories = ref([])

// 计算当前选中的知识库（根据搜索来源类型和当前Tab页）
const selectedKnowledge = computed(() => {
  // 如果同时选择了企业和个人知识库，根据当前激活的Tab页显示
  if (searchSources.value.includes('enterprise') && searchSources.value.includes('personal')) {
    if (activeKnowledgeTab.value === 'enterprise') {
      return selectedEnterpriseKnowledge.value
    } else {
      return selectedPersonalKnowledge.value
    }
  }

  // 如果只选择了一种知识库，显示对应的选择
  if (searchSources.value.includes('enterprise')) {
    return selectedEnterpriseKnowledge.value
  }

  if (searchSources.value.includes('personal')) {
    return selectedPersonalKnowledge.value
  }

  return []
})

// 计算所有选中的知识库（用于发送消息时的验证）
const allSelectedKnowledge = computed(() => {
  const result = []
  if (searchSources.value.includes('enterprise')) {
    result.push(...selectedEnterpriseKnowledge.value)
  }
  if (searchSources.value.includes('personal')) {
    result.push(...selectedPersonalKnowledge.value)
  }
  return result
})

// 检查知识库选择是否完整
const isKnowledgeSelectionComplete = computed(() => {
  const hasEnterprise = searchSources.value.includes('enterprise')
  const hasPersonal = searchSources.value.includes('personal')

  // 如果没有选择任何知识库，返回true（不需要选择）
  if (!hasEnterprise && !hasPersonal) {
    return true
  }

  // 如果选择了企业知识库，必须有企业知识库的选择
  if (hasEnterprise && selectedEnterpriseKnowledge.value.length === 0) {
    return false
  }

  // 如果选择了个人知识库，必须有个人知识库的选择
  if (hasPersonal && selectedPersonalKnowledge.value.length === 0) {
    return false
  }

  return true
})

// 获取知识库选择提示文本
const getKnowledgeSelectionTip = () => {
  const hasEnterprise = searchSources.value.includes('enterprise')
  const hasPersonal = searchSources.value.includes('personal')
  const enterpriseSelected = selectedEnterpriseKnowledge.value.length > 0
  const personalSelected = selectedPersonalKnowledge.value.length > 0

  if (hasEnterprise && hasPersonal) {
    if (!enterpriseSelected && !personalSelected) {
      return '请在企业知识库和个人知识库中分别选择分类'
    } else if (!enterpriseSelected) {
      return '请切换到企业知识库Tab页并选择分类'
    } else if (!personalSelected) {
      return '请切换到个人知识库Tab页并选择分类'
    }
  } else if (hasEnterprise && !enterpriseSelected) {
    return '请在左侧选择企业知识库分类'
  } else if (hasPersonal && !personalSelected) {
    return '请在左侧选择个人知识库分类'
  }

  return '请在左侧选择知识库分类'
}
const searchSourceOptions = [
  { value: 'web', label: '联网', icon: Connection },
  { value: 'personal', label: '个人知识库', icon: User },
  { value: 'enterprise', label: '企业知识库', icon: OfficeBuilding },
]

// 计算属性：根据搜索来源确定是否开启联网搜索
const isWebSearch = computed(() => searchSources.value.includes('web'))

// 监听搜索来源变化，提供用户反馈
watch(searchSources, (newValue, oldValue) => {
  console.log(`搜索来源已切换到：${newValue.join(', ')}`)

  // 如果搜索来源发生变化，重新初始化展开状态
  if (oldValue && JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
    // 清除展开状态，因为不同知识库有不同的分类结构
    expandedCategories.value = []
    expandedItems.value = []

    // 重新初始化展开状态
    initializeExpandedState()

    // 提示用户
    const sourceNames = newValue.map(source => {
      const sourceMap: Record<string, string> = {
        personal: '个人知识库',
        enterprise: '企业知识库',
        web: '联网搜索'
      }
      return sourceMap[source]
    }).join(' + ')

    if (sourceNames) {
      ElMessage.info(`已切换到：${sourceNames}`)
    }
  }

  // 如果是初始加载，也需要初始化展开状态
  if (!oldValue) {
    initializeExpandedState()
  }

  // 设置默认的激活tab页
  if (newValue.includes('enterprise') && newValue.includes('personal')) {
    // 如果同时选择了企业和个人知识库，默认激活企业知识库
    if (!activeKnowledgeTab.value || (!newValue.includes(activeKnowledgeTab.value))) {
      activeKnowledgeTab.value = 'enterprise'
    }
  } else if (newValue.includes('enterprise')) {
    activeKnowledgeTab.value = 'enterprise'
  } else if (newValue.includes('personal')) {
    activeKnowledgeTab.value = 'personal'
  }
}, { deep: true })

// 计算发送按钮是否应该被禁用
const isSendDisabled = computed(() => {
  // 如果正在输入中，按钮应该可点击（用于停止生成）
  if (isTyping.value) {
    return false
  }

  // 基本条件：没有输入内容且没有文件
  const basicDisabled = !inputMessage.value.trim() && uploadedFiles.value.length === 0

  // 知识库选择条件：如果选择了个人或企业知识库，必须完整选择分类
  const knowledgeDisabled = (searchSources.value.includes('personal') || searchSources.value.includes('enterprise')) && !isKnowledgeSelectionComplete.value

  return basicDisabled || knowledgeDisabled
})

const selectedModel = ref('gpt-3.5')
const modelOptions = [
  { value: 'gpt-3.5', label: 'GPT-3.5（OpenAI）' },
  { value: 'gpt-4', label: 'GPT-4（OpenAI）' },
  { value: 'wenxin', label: '文心一言（百度）' },
  { value: 'qwen', label: '通义千问（阿里）' },
  { value: 'deepseek', label: 'DeepSeek' },
  { value: 'custom', label: '自定义模型' }
]

const enterpriseOptions = ref<KnowledgeOption[]>([
  // 外部知识库选项
  { id: 'national-construction', name: '国家规范' },
  { id: 'industry-construction', name: '行业规范' },
  { id: 'local-construction', name: '地方规范' },
  { id: 'national-atlas', name: '国家图集' },
  { id: 'industry-atlas', name: '行业图集' },
  { id: 'local-atlas', name: '地方图集' },
  { id: 'electrical', name: '电气设计规范' },
  { id: 'building', name: '建筑设计规范' },
  { id: 'structure', name: '结构设计规范' },
  { id: 'heating', name: '暖通设计规范' },
  { id: 'water', name: '给排水设计规范' },

  // 内部知识库选项
  { id: 'holding-regulations', name: '控股集团制度' },
  { id: 'holding-standards', name: '控股集团标准' },
  { id: 'holding-policies', name: '控股集团办法及通知' },
  { id: 'group-regulations', name: '建工集团制度' },
  { id: 'group-standards', name: '建工集团标准' },
  { id: 'group-policies', name: '建工集团办法及通知' },

  // 十一建企业制度汇编
  { id: 'business-category', name: '经营类制度' },
  { id: 'production-category', name: '生产类制度' },
  { id: 'safety-category', name: '安全类制度' },
  { id: 'technology-achievement', name: '科技成果类制度' },
  { id: 'technical-quality', name: '技术质量类制度' },
  { id: 'business-affairs', name: '商务类制度' },
  { id: 'finance-category', name: '财务类制度' },
  { id: 'hr-management', name: '人力资源与干部管理类制度' },
  { id: 'administrative', name: '行政类制度' },
  { id: 'party-discipline', name: '党风廉政建设与纪检监察类制度' },
  { id: 'audit-category', name: '审计类制度' },
  { id: 'worker-rights', name: '职工权益保障类制度' },
  { id: 'design-category', name: '设计类制度' },
  { id: 'comprehensive-management', name: '综合管理类制度' },

  // 其他专业知识
  { id: 'enterprise-internal-control', name: '企业内部控制' },
  { id: 'standardization-manual', name: '标准化手册' },
  { id: 'process-standards-guide', name: '工艺标准指南' },
  { id: 'papers', name: '论文' },
  { id: 'patents', name: '专利' },
  { id: 'construction-methods', name: '工法' },
  { id: 'enterprise-culture-type', name: '企业文化类案例' },
  { id: 'party-innovation-type', name: '党建创新类案例' },
  { id: 'case-review-results', name: '案例复盘成果' },
  { id: 'international-building-standards', name: '国际建筑说明通用规范' }
])

const personalOptions = ref<KnowledgeOption[]>([
  // 个人学习笔记
  { id: 'personal-study-notes', name: '学习笔记' },
  { id: 'personal-project-experience', name: '项目经验总结' },
  { id: 'personal-technical-insights', name: '技术心得' },
  { id: 'personal-problem-solutions', name: '问题解决方案' },

  // 个人收藏资料
  { id: 'personal-bookmarks', name: '收藏的技术文章' },
  { id: 'personal-tutorials', name: '教程和指南' },
  { id: 'personal-tools', name: '常用工具清单' },
  { id: 'personal-templates', name: '模板和示例' },

  // 个人工作记录
  { id: 'personal-daily-logs', name: '工作日志' },
  { id: 'personal-meeting-notes', name: '会议记录' },
  { id: 'personal-task-tracking', name: '任务跟踪' },
  { id: 'personal-performance-review', name: '绩效总结' },

  // 个人技能发展
  { id: 'personal-skill-assessment', name: '技能评估' },
  { id: 'personal-learning-plan', name: '学习计划' },
  { id: 'personal-certification', name: '证书和认证' },
  { id: 'personal-career-planning', name: '职业规划' }
])

interface SearchSource {
  title: string
  content: string
  url?: string
  category?: string
}

interface Message {
  type: 'user' | 'assistant'
  content: string
  time: string
  files?: UploadedFile[]
  searchSources?: SearchSource[]
  originalContent?: string  // 保存原文内容
  isTranslated?: boolean    // 标记是否已翻译
}

interface KnowledgeOption {
  id: string
  name: string
}

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  file: File
}

function triggerFileUpload() {
  console.log('点击上传按钮，fileInputRef:', fileInputRef.value) // 调试用
  if (fileInputRef.value) {
    fileInputRef.value.click()
  } else {
    console.error('fileInputRef 未找到')
  }
}
// 处理拖拽悬停
function handleDragOver(event: DragEvent) {
  event.preventDefault()
  isDragOver.value = true
}

function handleDragEnter(event: DragEvent) {
  event.preventDefault()
  isDragOver.value = true
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault()
  // 只有当离开整个输入区域时才设置为false
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY
  
  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false
  }
}

function handleFileUpload(event: Event) {
  console.log('文件上传事件触发') // 调试用
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (files && files.length > 0) {
    console.log('选择的文件:', files) // 调试用
    Array.from(files).forEach(file => {
      // 验证文件大小 (10MB限制)
      if (file.size > 10 * 1024 * 1024) {
        ElMessage.error(`文件 ${file.name} 大小超过10MB限制`)
        return
      }
      
      // 验证文件类型
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv'
      ]
      
      if (!allowedTypes.includes(file.type)) {
        ElMessage.error(`不支持的文件类型: ${file.name}`)
        return
      }
      
      const uploadedFile: UploadedFile = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        file: file
      }
      uploadedFiles.value.push(uploadedFile)
      console.log('文件添加成功:', uploadedFile) // 调试用
    })
    
    ElMessage.success(`成功添加 ${files.length} 个文件`)
  }
  
  // 清空文件输入框
  if (target) {
    target.value = ''
  }
}

// 添加文件到上传列表
function addFileToUpload(file: File) {
  // 验证文件大小
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error(`文件 ${file.name} 大小超过10MB限制`)
    return
  }
  
  const uploadedFile: UploadedFile = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    name: file.name,
    size: file.size,
    type: file.type,
    file: file
  }
  uploadedFiles.value.push(uploadedFile)
}

const isDragOver = ref(false)

function handleDrop(event: DragEvent) {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (files) {
    Array.from(files).forEach(file => {
      addFileToUpload(file)
    })
    
    if (files.length > 0) {
      ElMessage.success(`成功拖拽 ${files.length} 个文件`)
    }
  }
}

function removeFile(fileId: string) {
  const index = uploadedFiles.value.findIndex(file => file.id === fileId)
  if (index > -1) {
    uploadedFiles.value.splice(index, 1)
    ElMessage.success('文件已移除')
  }
}

function isImageFile(file: UploadedFile): boolean {
  return file.type.startsWith('image/')
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function getFileDisplayName(fileName: string): string {
  // 移除文件扩展名，只显示文件名
  const lastDotIndex = fileName.lastIndexOf('.')
  if (lastDotIndex > 0) {
    return fileName.substring(0, lastDotIndex)
  }
  return fileName
}

function getCurrentTime() {
  return new Date().toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

function scrollToBottom() {
  nextTick(() => {
    if (chatContentRef.value) {
      chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
    }
  })
}

function askQuestion(question: string) {
  inputMessage.value = question
  sendMessage()
}

function sendMessage() {
  if ((!inputMessage.value.trim() && uploadedFiles.value.length === 0) || isTyping.value) return

  // 验证知识库选择
  if ((searchSources.value.includes('personal') || searchSources.value.includes('enterprise')) && !isKnowledgeSelectionComplete.value) {
    ElMessage.warning(getKnowledgeSelectionTip())
    return
  }

  const userMessage: Message = {
    type: 'user',
    content: inputMessage.value || '发送了文件',
    time: getCurrentTime(),
    files: uploadedFiles.value.length > 0 ? [...uploadedFiles.value] : undefined
  }
  
  messages.value.push(userMessage)
  inputMessage.value = ''
  uploadedFiles.value = []
  
  scrollToBottom()
  
  // 模拟AI回复
  isTyping.value = true
  const typingTimeout = setTimeout(() => {
    if (isTyping.value) { // 检查是否被停止
      let responses: string[] = []

      // 根据搜索来源提供不同的回复
      // 根据多选的搜索来源提供不同的回复
      const selectedSources = searchSources.value
      if (selectedSources.includes('personal') && selectedSources.includes('enterprise') && selectedSources.includes('web')) {
        responses = [
          '我正在从个人知识库、企业知识库和联网搜索中综合查找信息...',
          '正在整合个人经验、企业标准和最新网络资料为您提供答案...'
        ]
      } else if (selectedSources.includes('personal') && selectedSources.includes('enterprise')) {
        responses = [
          '我正在从个人知识库和企业知识库中搜索相关信息...',
          '正在结合个人经验和企业标准为您提供专业建议...'
        ]
      } else if (selectedSources.includes('personal') && selectedSources.includes('web')) {
        responses = [
          '我正在从个人知识库和联网搜索中查找信息...',
          '正在结合个人经验和最新网络资料为您整理答案...'
        ]
      } else if (selectedSources.includes('enterprise') && selectedSources.includes('web')) {
        responses = [
          '我正在从企业知识库和联网搜索中查找信息...',
          '正在结合企业标准和最新网络资料为您提供答案...'
        ]
      } else if (selectedSources.includes('personal')) {
        responses = [
          '我已经从您的个人知识库中搜索相关信息，正在为您整理答案...',
          '基于您个人知识库的内容，我来为您详细解答...'
        ]
      } else if (selectedSources.includes('enterprise')) {
        responses = [
          '我已经从企业知识库中搜索相关信息，正在为您整理答案...',
          '基于企业知识库的内容，我来为您详细解答...'
        ]
      } else if (selectedSources.includes('web')) {
        responses = [
          '我已经通过联网搜索获取最新信息，正在为您整理答案...',
          '基于联网搜索的结果，我来为您详细解答...'
        ]
      } else {
        responses = [
          '我已经收到您的问题，正在分析中...',
          '根据您的问题，我来为您详细解答...'
        ]
      }

      // 生成搜索来源数据
      const generateSearchSources = (): SearchSource[] => {
        const sources: SearchSource[] = []

        if (searchSources.value.includes('enterprise')) {
          // 企业知识库来源 - 正式的企业文档和标准
          sources.push(
            {
              title: '12.5.3施工组织设计的内容',
              content: '土木工程施工-武汉理工大学出版社',
              category: '企业标准'
            },
            {
              title: '管道施工技术规范 GB50235-2010',
              content: '建筑工程技术标准-国家标准',
              category: '技术规范'
            }
          )
        }

        if (searchSources.value.includes('personal')) {
          // 个人知识库来源 - 个人经验和学习笔记
          sources.push(
            {
              title: '管道焊接实战经验总结',
              content: '个人项目经验总结-张工程师',
              category: '个人笔记'
            },
            {
              title: '施工现场常见问题及解决方案',
              content: '个人工作日志-问题解决记录',
              category: '经验分享'
            }
          )
        }

        if (searchSources.value.includes('web')) {
          // 联网搜索来源
          sources.push(
            {
              title: '深入了解如何使用BILIBLI AJU...',
              content: 'CSDN 专业开发者社区',
              url: 'http://apt.qmzhiku.com/knowledge...'
            },
            {
              title: '施工技术最新发展趋势',
              content: '建筑工程网',
              url: 'http://www.construction.com/...'
            }
          )
        }

        return sources
      }

      const assistantMessage: Message = {
        type: 'assistant',
        content: responses[Math.floor(Math.random() * responses.length)],
        time: getCurrentTime(),
        searchSources: generateSearchSources()
      }

      messages.value.push(assistantMessage)
      isTyping.value = false
      scrollToBottom()
    }
  }, 2000)
  
  // 保存timeout ID以便停止时清除
  currentTypingTimeout.value = typingTimeout
}

function toggleKnowledge(id: string) {
  // 根据当前选择的搜索来源，确定应该操作哪个知识库
  // 如果同时选择了企业和个人，需要判断这个id属于哪个知识库
  const isEnterpriseItem = enterpriseOptions.value.some(opt => opt.id === id)
  const isPersonalItem = personalOptions.value.some(opt => opt.id === id)

  if (isEnterpriseItem && searchSources.value.includes('enterprise')) {
    const index = selectedEnterpriseKnowledge.value.indexOf(id)
    if (index > -1) {
      selectedEnterpriseKnowledge.value.splice(index, 1)
    } else {
      selectedEnterpriseKnowledge.value.push(id)
    }
  } else if (isPersonalItem && searchSources.value.includes('personal')) {
    const index = selectedPersonalKnowledge.value.indexOf(id)
    if (index > -1) {
      selectedPersonalKnowledge.value.splice(index, 1)
    } else {
      selectedPersonalKnowledge.value.push(id)
    }
  }
}

function removeKnowledge(id: string) {
  if (searchSource.value === 'enterprise') {
    const index = selectedEnterpriseKnowledge.value.indexOf(id)
    if (index > -1) {
      selectedEnterpriseKnowledge.value.splice(index, 1)
    }
  } else if (searchSource.value === 'personal') {
    const index = selectedPersonalKnowledge.value.indexOf(id)
    if (index > -1) {
      selectedPersonalKnowledge.value.splice(index, 1)
    }
  }
}

function getKnowledgeName(id: string) {
  const allOptions = [...enterpriseOptions.value, ...personalOptions.value]
  const option = allOptions.find(opt => opt.id === id)
  return option?.name || id
}

// 获取搜索来源卡片样式类
const getSourceCardClass = (category?: string) => {
  // if (!category) return ''

  // 企业知识库相关分类
/*   if (['企业标准', '技术规范', '质量管理', '安全标准', '技术标准', '施工指南', '安全管理'].includes(category)) {
    return 'enterprise-source'
  } */

  // 个人知识库相关分类
 /*  if (['个人笔记', '经验分享', '学习笔记', '管理心得'].includes(category)) {
    return 'personal-source'
  } */

  return 'enterprise-source'
}

// 获取搜索来源分类标签样式类
const getSourceCategoryClass = (category?: string) => {
  // if (!category) return ''

  // 企业知识库相关分类
  /* if (['企业标准', '技术规范', '质量管理', '安全标准', '技术标准', '施工指南', '安全管理'].includes(category)) {
    return 'enterprise-category'
  } */

  // 个人知识库相关分类
  /* if (['个人笔记', '经验分享', '学习笔记', '管理心得'].includes(category)) {
    return 'personal-category'
  } */

  return 'enterprise-category'
}

const textareaRef = ref<HTMLTextAreaElement>()

function adjustTextareaHeight() {
  if (textareaRef.value) {
    textareaRef.value.style.height = 'auto'
    textareaRef.value.style.height = textareaRef.value.scrollHeight + 'px'
  }
}

// 复制消息
const copyMessage = async (content: string) => {
  try {
    // 移除HTML标签，只复制纯文本
    const textContent = content.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ')
    await navigator.clipboard.writeText(textContent)
    ElMessage.success('复制成功')
  } catch (error) {
    // 如果clipboard API不可用，使用fallback方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = content.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ')
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('复制成功')
    } catch {
      ElMessage.error('复制失败，请手动选择文本复制')
    }
  }
}

// 下载消息
const downloadMessage = (content: string) => {
  try {
    // 移除HTML标签，保留纯文本格式
    const textContent = content
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<\/p>/gi, '\n')
      .replace(/<p[^>]*>/gi, '')
      .replace(/<h[1-6][^>]*>/gi, '\n')
      .replace(/<\/h[1-6]>/gi, '\n')
      .replace(/<li[^>]*>/gi, '• ')
      .replace(/<\/li>/gi, '\n')
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\n\s*\n/g, '\n\n') // 清理多余的空行
      .trim()

    const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `智能问答_${new Date().toLocaleString().replace(/[/:]/g, '-')}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败，请重试')
  }
}

// 切换翻译状态
const toggleTranslation = (message: Message) => {
  if (message.isTranslated) {
    // 返回原文
    if (message.originalContent) {
      message.content = message.originalContent
      message.isTranslated = false
      ElMessage.success('已返回原文')
    }
  } else {
    // 翻译内容
    ElMessage.info('正在翻译...')

    setTimeout(() => {
      // 保存原文
      message.originalContent = message.content

      // 翻译内容
      const translatedContent = translateContent(message.content)
      message.content = translatedContent
      message.isTranslated = true

      ElMessage.success('翻译完成')
    }, 1500)
  }
}

// 翻译内容函数
const translateContent = (content: string): string => {
  // 这里可以集成真实的翻译API，比如百度翻译、谷歌翻译等
  // 现在提供更完整的模拟翻译

  const translations: { [key: string]: string } = {
    // 标题翻译
    '管道施工基本流程': 'Basic Pipeline Construction Process',
    '施工准备阶段': 'Construction Preparation Phase',
    '施工实施阶段': 'Construction Implementation Phase',
    '验收阶段': 'Acceptance Phase',
    '焊接工艺标准要求': 'Welding Process Standard Requirements',
    '焊接前准备': 'Pre-welding Preparation',
    '焊接过程控制': 'Welding Process Control',
    '安全防护措施': 'Safety Protection Measures',
    '质量控制要点': 'Quality Control Points',

    // 内容翻译
    '图纸审查': 'Drawing Review',
    '详细审查施工图纸和技术规范': 'Detailed review of construction drawings and technical specifications',
    '材料检验': 'Material Inspection',
    '检查材料质量证明文件': 'Check material quality certification documents',
    '现场确认': 'Site Confirmation',
    '确认施工现场条件和安全措施': 'Confirm construction site conditions and safety measures',
    '管道预制和组装': 'Pipeline Prefabrication and Assembly',
    '管道安装和连接': 'Pipeline Installation and Connection',
    '焊接作业': 'Welding Operations',
    '执行焊接工艺规程': 'Execute welding process procedures',
    '质量检验和测试': 'Quality Inspection and Testing',
    '压力试验': 'Pressure Test',
    '防腐检查': 'Anti-corrosion Inspection',
    '竣工验收': 'Completion Acceptance',
    '重要提醒': 'Important Reminder',
    '严格按照验收标准执行': 'Strictly follow acceptance standards',
    '安全第一': 'Safety First',
    '施工过程中请严格遵守安全操作规程': 'Please strictly follow safety operating procedures during construction',

    // 通用词汇
    '您好': 'Hello',
    '智能助手': 'Intelligent Assistant',
    '我可以帮助您': 'I can help you',
    '技术问题解答': 'Technical Problem Solving',
    '提示': 'Tips'
  }

  let translatedContent = content

  // 按照长度排序，优先匹配长的短语
  const sortedTranslations = Object.entries(translations).sort((a, b) => b[0].length - a[0].length)

  sortedTranslations.forEach(([chinese, english]) => {
    const regex = new RegExp(chinese, 'g')
    translatedContent = translatedContent.replace(regex, english)
  })

  // 如果内容基本没有变化，说明没有匹配的翻译
  const originalText = content.replace(/<[^>]*>/g, '').trim()
  const translatedText = translatedContent.replace(/<[^>]*>/g, '').trim()

  if (originalText === translatedText && originalText.length > 0) {
    // 返回一个通用的英文翻译提示
    return content.replace(originalText, 'English Translation: This content requires professional translation service integration.')
  }

  return translatedContent
}

// 删除消息
const deleteMessage = (message: Message) => {
  const index = messages.value.findIndex(msg => msg.time === message.time)
  if (index > -1) {
    messages.value.splice(index, 1)
    ElMessage.success('删除成功')
  }
}

// 扩写消息
const expandMessage = (message: Message) => {
  ElMessage.info('正在扩写内容...')

  setTimeout(() => {
    const expandedContent = expandContent(message.content)
    const expandedMessage: Message = {
      type: 'assistant',
      content: expandedContent,
      time: getCurrentTime()
    }

    // 在原消息后添加扩写版本
    const index = messages.value.findIndex(msg => msg.time === message.time)
    if (index > -1) {
      messages.value.splice(index + 1, 0, expandedMessage)
    } else {
      messages.value.push(expandedMessage)
    }

    scrollToBottom()
    ElMessage.success('扩写完成')
  }, 2000)
}

// 扩写内容函数
const expandContent = (originalContent: string): string => {
  const textContent = originalContent.replace(/<[^>]*>/g, '').trim()

  if (textContent.includes('管道施工')) {
    return `# 📋 管道施工详细作业指导

## 前期准备阶段

### 🔍 技术准备
- **图纸会审**：组织设计、施工、监理等各方进行图纸会审
- **技术交底**：对施工班组进行详细的技术交底
- **方案编制**：编制专项施工方案和安全技术措施

### 📦 材料准备
- **材料验收**：严格按照标准验收管材、管件等材料
- **质量证明**：核查材质证明书、检验报告等文件
- **存储管理**：按要求分类存放，防止损坏和混用

## 施工实施阶段

### 🔧 管道预制
1. **精确下料**：根据施工图纸和现场实测尺寸进行下料
2. **坡口加工**：确保坡口角度、钝边符合焊接要求
3. **预制组对**：严格控制管道轴线和组对间隙
4. **预制焊接**：按照焊接工艺规程进行焊接作业

### 🏗️ 现场安装
1. **基础验收**：确认管道支撑基础符合设计要求
2. **管道就位**：使用合适的起重设备进行管道安装
3. **现场焊接**：严格控制焊接质量，做好层间清理
4. **质量检验**：进行外观检查和无损检测

## 质量控制要点

### ⚡ 焊接质量管理
- **焊工管理**：确保焊工持证上岗，技能符合要求
- **工艺控制**：严格按照焊接工艺规程执行
- **检验标准**：按照相关标准进行质量检验

### 🛡️ 安全防护措施
- **安全教育**：定期进行安全教育和技术培训
- **防护设备**：配备完善的安全防护用品
- **现场管理**：建立完善的现场安全管理制度

这份详细指导涵盖了管道施工的全过程，为确保工程质量提供了全面的技术支持。`
  }

  return `**【详细扩写版本】**

${textContent}

---

## 🔍 深度解析

### 技术背景
上述内容基于行业最佳实践和技术标准制定，结合了多年的工程经验和技术积累。

### 实施要点
1. **标准化操作**：建立标准化的作业程序和质量控制体系
2. **过程监控**：对关键工序进行全过程质量监控
3. **持续改进**：根据实施效果不断优化工艺流程

### 注意事项
- 严格遵守相关技术规范和安全标准
- 加强与各参建单位的沟通协调
- 做好施工记录和质量档案管理

### 预期效果
通过严格执行上述要求，可以确保工程质量达到设计标准，为后续运行维护奠定良好基础。`
}

// 简写消息
const summarizeMessage = (message: Message) => {
  ElMessage.info('正在简写内容...')

  setTimeout(() => {
    const summarizedContent = summarizeContent(message.content)
    const summarizedMessage: Message = {
      type: 'assistant',
      content: summarizedContent,
      time: getCurrentTime()
    }

    // 在原消息后添加简写版本
    const index = messages.value.findIndex(msg => msg.time === message.time)
    if (index > -1) {
      messages.value.splice(index + 1, 0, summarizedMessage)
    } else {
      messages.value.push(summarizedMessage)
    }

    scrollToBottom()
    ElMessage.success('简写完成')
  }, 1500)
}

// 简写内容函数
const summarizeContent = (originalContent: string): string => {
  const textContent = originalContent.replace(/<[^>]*>/g, '').trim()

  if (textContent.includes('管道施工')) {
    return `**📋 管道施工要点总结**

**准备阶段：**
- 图纸审查、技术交底
- 材料验收、设备检查

**施工阶段：**
- 管道预制、现场安装
- 焊接作业、质量检验

**控制要点：**
- 焊接质量、安装精度
- 安全防护、环境保护

**验收标准：**
- 压力试验、防腐检查
- 竣工验收、资料整理

💡 **核心提醒**：严格按规范施工，确保质量安全。`
  } else if (textContent.includes('管道维修')) {
    return `**🔧 管道维修核心要点**

**故障诊断：** 泄漏、腐蚀、损伤、松动
**维修方案：** 临时修复、永久修复
**作业流程：** 安全隔离→评估→维修→验收
**安全要求：** 介质排空、防护措施

⚠️ **关键提醒**：维修前确保安全，按规范操作。`
  } else if (textContent.includes('焊接施工')) {
    return `**⚡ 焊接施工安全要点**

**准备工作：** 环境检查、设备检查、人员资质
**过程控制：** 工艺参数、质量控制
**安全防护：** 防触电、防火防爆、防护用品
**应急处理：** 制定预案、及时响应

🚨 **安全第一**：严格防护，规范操作。`
  }

  // 通用简写逻辑
  const sentences = textContent.split(/[。！？.!?]/).filter(s => s.trim().length > 0)
  const keyPoints = sentences.slice(0, 3).map(s => s.trim()).join('；')

  return `**【简写版本】**

${keyPoints}

---

**核心要点：** 以上内容的关键信息已提炼，如需详细信息请参考完整版本。`
}

// 重新生成消息
const regenerateMessage = (message: Message) => {
  ElMessage.info('正在重新生成...')

  // 模拟重新生成功能
  setTimeout(() => {
    const regeneratedContent = generateAlternativeContent(message.content)
    const regeneratedMessage: Message = {
      type: 'assistant',
      content: regeneratedContent,
      time: getCurrentTime()
    }

    // 替换原消息
    const index = messages.value.findIndex(msg => msg.time === message.time)
    if (index > -1) {
      messages.value[index] = regeneratedMessage
    } else {
      messages.value.push(regeneratedMessage)
    }

    scrollToBottom()
    ElMessage.success('重新生成完成')
  }, 2000)
}

// 生成替代内容
const generateAlternativeContent = (originalContent: string): string => {
  // 移除HTML标签获取纯文本
  const textContent = originalContent.replace(/<[^>]*>/g, '').trim()

  // 根据内容类型生成不同的替代版本
  if (textContent.includes('管道施工')) {
    return `# 管道施工作业指导

## 🔧 施工准备阶段
**材料准备**
- 管材质量检验与验收
- 焊接材料性能确认
- 施工工具设备检查

**技术准备**
- 施工图纸会审
- 技术交底完成
- 作业指导书编制

## 🏗️ 施工实施阶段
1. **管道预制**：按图纸要求进行管道预制加工
2. **现场安装**：严格按照安装工艺进行施工
3. **焊接作业**：执行焊接工艺规程，确保焊接质量
4. **质量检验**：进行外观检查和无损检测

## ✅ 验收阶段
- 压力试验
- 防腐检查
- 竣工验收

> 💡 **温馨提示**：施工过程中请严格遵守安全操作规程，确保人员安全。`
  } else if (textContent.includes('管道维修')) {
    return `# 管道维修技术指南

## 🔍 故障诊断
**常见问题类型**
- 管道泄漏
- 腐蚀损坏
- 机械损伤
- 接头松动

## 🛠️ 维修方案
**临时修复**
- 夹具修复法
- 胶粘修复法
- 套管修复法

**永久修复**
- 管段更换
- 补焊修复
- 重新连接

## 📋 维修流程
1. **安全隔离**：关闭相关阀门，确保作业安全
2. **损坏评估**：确定维修方案和所需材料
3. **维修实施**：按照技术规范进行维修作业
4. **质量验收**：进行压力测试和质量检查

⚠️ **安全提醒**：维修前必须确认管道内介质已排空，并做好安全防护。`
  } else if (textContent.includes('焊接施工')) {
    return `# 焊接施工安全作业规程

## 🔥 焊接前准备
**环境检查**
- 作业环境通风良好
- 清除易燃易爆物品
- 配备消防器材

**设备检查**
- 焊接设备完好
- 电缆绝缘良好
- 接地可靠

## 👷 人员要求
- 持证上岗
- 穿戴防护用品
- 身体状况良好

## ⚡ 焊接过程控制
**工艺参数**
- 焊接电流
- 电弧电压
- 焊接速度
- 层间温度

**质量控制**
- 焊缝外观检查
- 尺寸测量
- 无损检测

## 🛡️ 安全防护措施
- 防触电措施
- 防火防爆措施
- 有毒气体防护
- 弧光辐射防护

🚨 **紧急情况处理**：如发生意外，立即停止作业，采取应急措施并报告相关部门。`
  }

  // 默认重新生成
  return `**重新生成的内容：**

${textContent}

---

**补充说明：**
以上内容已根据最新的技术标准和安全规范进行了重新整理，提供了更加详细和实用的指导信息。如需了解更多具体细节，请随时提问。`
}

// 停止生成功能
const stopGeneration = () => {
  if (isTyping.value && currentTypingTimeout.value) {
    clearTimeout(currentTypingTimeout.value)
    isTyping.value = false
    currentTypingTimeout.value = null
    ElMessage.info('已停止生成')
  }
}

// 添加timeout引用
const currentTypingTimeout = ref<number | null>(null)

// 企业知识库分类数据
const enterpriseKnowledgeCategories = ref([
  {
    id: 'shiyi-library',
    name: '十一智库-资料',
    icon: '📚',
    children: [
      {
        id: 'external',
        name: '外部',
        children: [
          {
            id: 'regulations',
            name: '规范、图集',
            children: [
              {
                id: 'construction-standards',
                name: '施工规范、图集',
                children: [
                  {
                    id: 'construction-regulations',
                    name: '施工规范',
                    children: [
                      { id: 'national-construction', name: '国家规范' },
                      { id: 'industry-construction', name: '行业规范' },
                      { id: 'local-construction', name: '地方规范' }
                    ]
                  },
                  {
                    id: 'construction-atlas',
                    name: '施工图集',
                    children: [
                      { id: 'national-atlas', name: '国家规范' },
                      { id: 'industry-atlas', name: '行业规范' },
                      { id: 'local-atlas', name: '地方规范' }
                    ]
                  }
                ]
              },
              {
                id: 'design-standards',
                name: '设计规范、图集',
                children: [
                  { id: 'electrical', name: '电' },
                  { id: 'building', name: '建筑' },
                  { id: 'structure', name: '结构' },
                  { id: 'heating', name: '暖' },
                  { id: 'water', name: '水' }
                ]
              }
            ]
          }
        ]
      },
      {
        id: 'internal',
        name: '内部',
        children: [
          {
            id: 'shaanxi-construction-holding',
            name: '陕西建工控股集团有限公司',
            children: [
              { id: 'holding-regulations', name: '制度' },
              { id: 'holding-standards', name: '标准' },
              { id: 'holding-policies', name: '相关办法及通知' }
            ]
          },
          {
            id: 'shaanxi-construction-group',
            name: '陕西建工集团股份有限公司',
            children: [
              { id: 'group-regulations', name: '制度' },
              { id: 'group-standards', name: '标准' },
              { id: 'group-policies', name: '相关办法及通知' }
            ]
          },
          {
            id: 'shaanxi-11th-construction',
            name: '陕西建工第十一建设集团有限公司',
            children: [
              {
                id: 'enterprise-system-compilation',
                name: '企业制度汇编',
                children: [
                  { id: 'business-category', name: '经营类' },
                  { id: 'production-category', name: '生产类' },
                  { id: 'safety-category', name: '安全类' },
                  { id: 'technology-achievement', name: '科技成果类' },
                  { id: 'technical-quality', name: '技术质量类' },
                  { id: 'business-affairs', name: '商务类' },
                  { id: 'finance-category', name: '财务类' },
                  { id: 'hr-management', name: '人力资源与干部管理类' },
                  { id: 'administrative', name: '行政类' },
                  { id: 'party-discipline', name: '党风廉政建设与纪检监察类' },
                  { id: 'audit-category', name: '审计类' },
                  { id: 'worker-rights', name: '职工权益保障类' },
                  { id: 'design-category', name: '设计类' },
                  { id: 'comprehensive-management', name: '综合管理类' }
                ]
              },
              {
                id: 'enterprise-internal-control',
                name: '企业内部控制'
              },
              {
                id: 'standardization-manual',
                name: '标准化手册'
              },
              {
                id: 'process-standards-guide',
                name: '工艺标准指南'
              },
              {
                id: 'professional-knowledge',
                name: '专业知识',
                children: [
                  {
                    id: 'tech-achievements',
                    name: '科技成果',
                    children: [
                      { id: 'papers', name: '论文' },
                      { id: 'patents', name: '专利' },
                      { id: 'construction-methods', name: '工法' }
                    ]
                  },
                  {
                    id: 'excellent-cases',
                    name: '优秀案例',
                    children: [
                      { id: 'enterprise-culture-type', name: '企业文化类' },
                      { id: 'party-innovation-type', name: '党建创新类' }
                    ]
                  },
                  { id: 'case-review-results', name: '案例复盘成果' },
                  { id: 'international-building-standards', name: '国际建筑说明通用规范' }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
])

// 个人知识库分类数据
const personalKnowledgeCategories = ref([
  {
    id: 'shiyi-library',
    name: '十一智库-资料',
    icon: '📚',
    children: [
      {
        id: 'external',
        name: '外部',
        children: [
          {
            id: 'regulations',
            name: '规范、图集',
            children: [
              {
                id: 'construction-standards',
                name: '施工规范、图集',
                children: [
                  {
                    id: 'construction-regulations',
                    name: '施工规范',
                    children: [
                      { id: 'national-construction', name: '国家规范' },
                      { id: 'industry-construction', name: '行业规范' },
                      { id: 'local-construction', name: '地方规范' }
                    ]
                  },
                  {
                    id: 'construction-atlas',
                    name: '施工图集',
                    children: [
                      { id: 'national-atlas', name: '国家规范' },
                      { id: 'industry-atlas', name: '行业规范' },
                      { id: 'local-atlas', name: '地方规范' }
                    ]
                  }
                ]
              },
              {
                id: 'design-standards',
                name: '设计规范、图集',
                children: [
                  { id: 'electrical', name: '电' },
                  { id: 'building', name: '建筑' },
                  { id: 'structure', name: '结构' },
                  { id: 'heating', name: '暖' },
                  { id: 'water', name: '水' }
                ]
              }
            ]
          }
        ]
      },
      {
        id: 'internal',
        name: '内部',
        children: [
          {
            id: 'shaanxi-construction-holding',
            name: '陕西建工控股集团有限公司',
            children: [
              { id: 'holding-regulations', name: '制度' },
              { id: 'holding-standards', name: '标准' },
              { id: 'holding-policies', name: '相关办法及通知' }
            ]
          },
          {
            id: 'shaanxi-construction-group',
            name: '陕西建工集团股份有限公司',
            children: [
              { id: 'group-regulations', name: '制度' },
              { id: 'group-standards', name: '标准' },
              { id: 'group-policies', name: '相关办法及通知' }
            ]
          },
          {
            id: 'shaanxi-11th-construction',
            name: '陕西建工第十一建设集团有限公司',
            children: [
              {
                id: 'enterprise-system-compilation',
                name: '企业制度汇编',
                children: [
                  { id: 'business-category', name: '经营类' },
                  { id: 'production-category', name: '生产类' },
                  { id: 'safety-category', name: '安全类' },
                  { id: 'technology-achievement', name: '科技成果类' },
                  { id: 'technical-quality', name: '技术质量类' },
                  { id: 'business-affairs', name: '商务类' },
                  { id: 'finance-category', name: '财务类' },
                  { id: 'hr-management', name: '人力资源与干部管理类' },
                  { id: 'administrative', name: '行政类' },
                  { id: 'party-discipline', name: '党风廉政建设与纪检监察类' },
                  { id: 'audit-category', name: '审计类' },
                  { id: 'worker-rights', name: '职工权益保障类' },
                  { id: 'design-category', name: '设计类' },
                  { id: 'comprehensive-management', name: '综合管理类' }
                ]
              },
              {
                id: 'enterprise-internal-control',
                name: '企业内部控制'
              },
              {
                id: 'standardization-manual',
                name: '标准化手册'
              },
              {
                id: 'process-standards-guide',
                name: '工艺标准指南'
              },
              {
                id: 'professional-knowledge',
                name: '专业知识',
                children: [
                  {
                    id: 'tech-achievements',
                    name: '科技成果',
                    children: [
                      { id: 'papers', name: '论文' },
                      { id: 'patents', name: '专利' },
                      { id: 'construction-methods', name: '工法' }
                    ]
                  },
                  {
                    id: 'excellent-cases',
                    name: '优秀案例',
                    children: [
                      { id: 'enterprise-culture-type', name: '企业文化类' },
                      { id: 'party-innovation-type', name: '党建创新类' }
                    ]
                  },
                  { id: 'case-review-results', name: '案例复盘成果' },
                  { id: 'international-building-standards', name: '国际建筑说明通用规范' }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
])

// 根据搜索来源和当前Tab页动态获取知识库分类
const knowledgeCategories = computed(() => {
  let categories = []

  // 如果同时选择了企业和个人知识库，根据当前激活的Tab页显示
  if (searchSources.value.includes('enterprise') && searchSources.value.includes('personal')) {
    if (activeKnowledgeTab.value === 'enterprise') {
      categories = enterpriseKnowledgeCategories.value
    } else {
      categories = personalKnowledgeCategories.value
    }
  } else if (searchSources.value.includes('enterprise')) {
    categories = enterpriseKnowledgeCategories.value
  } else if (searchSources.value.includes('personal')) {
    categories = personalKnowledgeCategories.value
  }

  // 如果有搜索关键词，进行过滤
  if (searchKeyword.value.trim()) {
    return filterCategories(categories, searchKeyword.value.trim())
  }

  return categories
})

// 递归过滤分类函数
const filterCategories = (categories: any[], keyword: string): any[] => {
  const filtered = []

  for (const category of categories) {
    // 检查当前分类名称是否匹配
    const nameMatch = category.name.toLowerCase().includes(keyword.toLowerCase())

    // 递归检查子分类
    let filteredChildren = []
    if (category.children) {
      filteredChildren = filterCategories(category.children, keyword)
    }

    // 如果当前分类名称匹配或有匹配的子分类，则包含此分类
    if (nameMatch || filteredChildren.length > 0) {
      filtered.push({
        ...category,
        children: filteredChildren.length > 0 ? filteredChildren : category.children
      })
    }
  }

  return filtered
}

// 处理搜索
const handleSearch = () => {
  // 搜索功能已经通过计算属性knowledgeCategories实现
  // 当searchKeyword变化时，knowledgeCategories会自动重新计算
  console.log('搜索关键词:', searchKeyword.value)

  // 如果有搜索结果，自动展开所有匹配的分类
  if (searchKeyword.value.trim()) {
    expandSearchResults()
  }
}

// 展开搜索结果
const expandSearchResults = () => {
  const expandIds = []

  // 递归收集所有匹配的分类ID
  const collectExpandIds = (categories: any[]) => {
    for (const category of categories) {
      expandIds.push(category.id)
      if (category.children) {
        collectExpandIds(category.children)
      }
    }
  }

  collectExpandIds(knowledgeCategories.value)
  expandedCategories.value = [...new Set([...expandedCategories.value, ...expandIds])]
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  // 重置展开状态到初始状态
  initializeExpandedState()
}

// 递归获取所有叶子节点（可选择的项目）
const getAllLeafItems = (items: any[]): string[] => {
  const leafItems: string[] = []

  items.forEach(item => {
    if (item.children && item.children.length > 0) {
      // 如果有子项，递归获取子项的叶子节点
      leafItems.push(...getAllLeafItems(item.children))
    } else {
      // 如果没有子项，这就是叶子节点
      leafItems.push(item.id)
    }
  })

  return leafItems
}

// 计算所有可选项（所有叶子节点）
const allKnowledgeItems = computed(() => {
  const allItems: string[] = []

  knowledgeCategories.value.forEach(category => {
    if (category.children && category.children.length > 0) {
      allItems.push(...getAllLeafItems(category.children))
    }
  })

  return allItems
})

// 判断是否全选
const isAllSelected = computed(() => {
  return allKnowledgeItems.value.length > 0 && 
         allKnowledgeItems.value.every(id => selectedKnowledge.value.includes(id))
})

// 全选功能
const selectAll = () => {
  // 如果同时选择了企业和个人知识库，根据当前激活的Tab页操作
  if (searchSources.value.includes('enterprise') && searchSources.value.includes('personal')) {
    if (activeKnowledgeTab.value === 'enterprise') {
      selectedEnterpriseKnowledge.value = [...allKnowledgeItems.value]
    } else {
      selectedPersonalKnowledge.value = [...allKnowledgeItems.value]
    }
  } else if (searchSources.value.includes('enterprise')) {
    selectedEnterpriseKnowledge.value = [...allKnowledgeItems.value]
  } else if (searchSources.value.includes('personal')) {
    selectedPersonalKnowledge.value = [...allKnowledgeItems.value]
  }
}

// 清除选择
const clearSelection = () => {
  // 如果同时选择了企业和个人知识库，根据当前激活的Tab页操作
  if (searchSources.value.includes('enterprise') && searchSources.value.includes('personal')) {
    if (activeKnowledgeTab.value === 'enterprise') {
      selectedEnterpriseKnowledge.value = []
    } else {
      selectedPersonalKnowledge.value = []
    }
  } else if (searchSources.value.includes('enterprise')) {
    selectedEnterpriseKnowledge.value = []
  } else if (searchSources.value.includes('personal')) {
    selectedPersonalKnowledge.value = []
  }
}

// 切换全选状态
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    clearSelection()
  } else {
    selectAll()
  }
}

// 统一的展开状态管理
const expandedCategories = ref<string[]>([]) // 展开的分类
const expandedItems = ref<string[]>([]) // 统一管理所有展开的项目

// 初始化默认展开状态
const initializeExpandedState = () => {
  const expanded = []
  if (searchSources.value.includes('enterprise')) {
    // 企业知识库默认展开第一级
    expanded.push('shiyi-library')
  }
  if (searchSources.value.includes('personal')) {
    // 个人知识库默认展开第一级
    expanded.push('personal-learning', 'personal-work', 'personal-resources')
  }
  expandedCategories.value = expanded
}

// 获取所有展开的项目
const getAllExpandedItems = () => {
  return [...expandedCategories.value, ...expandedItems.value]
}

// 处理展开/收起
const handleToggleExpand = (itemId: string) => {
  const index = expandedItems.value.indexOf(itemId)
  if (index > -1) {
    expandedItems.value.splice(index, 1)
  } else {
    expandedItems.value.push(itemId)
  }
}

// 处理选择
const handleToggleSelection = (item: any) => {
  if (item.children && item.children.length > 0) {
    toggleParentSelection(item)
  } else {
    toggleKnowledge(item.id)
  }
}

// 切换一级分类展开状态
const toggleCategory = (categoryId: string) => {
  const index = expandedCategories.value.indexOf(categoryId)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(categoryId)
  }
}

// 这些函数已被统一的 handleToggleExpand 替代

// 检查是否所有子项都被选中
const isAllChildrenSelected = (item: any): boolean => {
  if (!item.children || item.children.length === 0) return false
  return item.children.every((child: any) => {
    if (child.children && child.children.length > 0) {
      return isAllChildrenSelected(child)
    }
    return selectedKnowledge.value.includes(child.id)
  })
}

// 检查是否部分子项被选中
const isSomeChildrenSelected = (item: any): boolean => {
  if (!item.children || item.children.length === 0) return false
  return item.children.some((child: any) => {
    if (child.children && child.children.length > 0) {
      return isSomeChildrenSelected(child) || isAllChildrenSelected(child)
    }
    return selectedKnowledge.value.includes(child.id)
  })
}

// 获取某个父级项目下的所有叶子节点ID
const getChildrenLeafIds = (item: any): string[] => {
  if (!item.children || item.children.length === 0) {
    return [item.id]
  }

  const leafIds: string[] = []
  item.children.forEach((child: any) => {
    leafIds.push(...getChildrenLeafIds(child))
  })

  return leafIds
}

// 切换父级选择状态（选中/取消选中所有子项）
const toggleParentSelection = (item: any) => {
  const childrenIds = getChildrenLeafIds(item)
  const allSelected = isAllChildrenSelected(item)

  if (allSelected) {
    // 如果全部选中，则取消选中所有子项
    childrenIds.forEach(id => {
      const index = selectedKnowledge.value.indexOf(id)
      if (index > -1) {
        selectedKnowledge.value.splice(index, 1)
      }
    })
  } else {
    // 如果未全部选中，则选中所有子项
    childrenIds.forEach(id => {
      if (!selectedKnowledge.value.includes(id)) {
        selectedKnowledge.value.push(id)
      }
    })
  }
}

// 获取知识库分类数据的函数
const fetchKnowledgeCategories = async () => {
  try {
    // 这里替换为实际的API调用
    // const response = await api.getKnowledgeCategories()
    // knowledgeCategories.value = response.data
    
    // 目前使用mock数据
    console.log('使用Mock数据加载知识库分类')
  } catch (error) {
    console.error('获取知识库分类失败:', error)
    ElMessage.error('获取知识库分类失败')
  }
}

// 获取路由信息
const route = useRoute()

// 主题映射
const topicMap = {
  'pipeline-construction': '管道施工',
  'pipeline-maintenance': '管道维修',
  'welding-construction': '焊接施工注意'
}

// 防止重复处理主题的标志
const processedTopic = ref<string | null>(null)
// 获取主题相关的回复
const getTopicResponse = (topic: string): string => {
  switch (topic) {
    case 'pipeline-construction':
      return `<h1 class="message-heading message-h1">管道施工指南</h1>

<p>关于管道施工，我为您整理了以下要点：</p>

<h2 class="message-heading message-h2">1. 施工前准备</h2>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">图纸审查</strong>：详细审查施工图纸和技术规范</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">材料检验</strong>：检查材料质量证明文件</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">现场确认</strong>：确认施工现场条件和安全措施</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">人员配置</strong>：确保施工人员具备相应资质</span></div>

<h2 class="message-heading message-h2">2. 施工工艺流程</h2>
<div class="message-list-item message-ordered-item"><span class="message-list-number">1.</span><span class="message-list-content">管道预制和组装</span></div>
<div class="message-list-item message-ordered-item"><span class="message-list-number">2.</span><span class="message-list-content">管道安装和连接</span></div>
<div class="message-list-item message-ordered-item"><span class="message-list-number">3.</span><span class="message-list-content">压力测试和质量检验</span></div>
<div class="message-list-item message-ordered-item"><span class="message-list-number">4.</span><span class="message-list-content">防腐处理和保温</span></div>

<h2 class="message-heading message-h2">3. 质量控制要点</h2>
<blockquote class="message-quote"><strong class="message-bold">重要提醒</strong>：质量控制是管道施工的核心环节</blockquote>

<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">焊接质量控制</strong>：严格按照焊接工艺规程执行</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">防腐处理标准</strong>：确保防腐层厚度和附着力</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">安装精度要求</strong>：控制管道标高和坡度</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">检测验收</strong>：进行无损检测和压力试验</span></div>

<h2 class="message-heading message-h2">4. 安全注意事项</h2>
<p>⚠️ <strong class="message-bold">安全第一，预防为主</strong></p>

<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">施工现场安全防护</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">特殊作业安全措施</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">应急预案制定</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">定期安全检查</span></div>

<hr class="message-divider">

<p>💡 <strong class="message-bold">提示</strong>：您还想了解哪个具体方面的详细信息？我可以为您提供更深入的技术指导。</p>`

    case 'pipeline-maintenance':
      return `<p>关于管道维修，我为您介绍以下内容：</p>

<div class="message-list-item message-ordered-item"><span class="message-list-number">1.</span><span class="message-list-content"><strong class="message-bold">维修前检查</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">管道损坏程度评估</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">维修方案制定</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">安全隔离措施</span></div>

<div class="message-list-item message-ordered-item"><span class="message-list-number">2.</span><span class="message-list-content"><strong class="message-bold">常见维修方法</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">补焊修复</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">管段更换</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">套管修复</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">内衬修复</span></div>

<div class="message-list-item message-ordered-item"><span class="message-list-number">3.</span><span class="message-list-content"><strong class="message-bold">维修质量标准</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接质量要求</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">压力测试标准</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">防腐层修复</span></div>

<div class="message-list-item message-ordered-item"><span class="message-list-number">4.</span><span class="message-list-content"><strong class="message-bold">维修后验收</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">外观质量检查</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">压力试验</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">防腐层检测</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">运行测试</span></div>

<p>需要了解具体的维修技术或标准吗？</p>`

    case 'welding-construction':
      return `<p>关于焊接施工注意事项，请重点关注：</p>

<div class="message-list-item message-ordered-item"><span class="message-list-number">1.</span><span class="message-list-content"><strong class="message-bold">焊前准备</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊工资质确认</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接材料检验</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接设备检查</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">环境条件评估</span></div>

<div class="message-list-item message-ordered-item"><span class="message-list-number">2.</span><span class="message-list-content"><strong class="message-bold">焊接工艺控制</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接参数设定</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接顺序安排</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">层间温度控制</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊后热处理</span></div>

<div class="message-list-item message-ordered-item"><span class="message-list-number">3.</span><span class="message-list-content"><strong class="message-bold">质量控制措施</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接过程监控</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">外观质量检查</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">无损检测要求</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">返修处理程序</span></div>

<div class="message-list-item message-ordered-item"><span class="message-list-number">4.</span><span class="message-list-content"><strong class="message-bold">安全防护要求</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">个人防护用品</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">通风排烟措施</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">防火防爆措施</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">有毒气体防护</span></div>

<p>您想深入了解哪个方面的焊接技术？</p>`

    default:
      return '<p>您好！我是智能助手，很高兴为您服务。请问有什么可以帮助您的吗？</p>'
  }
} 

// 处理URL参数中的主题
const handleTopicFromUrl = () => {
  const topic = route.query.topic as string

  // 如果已经处理过相同的主题，则不重复处理
  if (topic === processedTopic.value) {
    return
  }

  if (topic && topicMap[topic as keyof typeof topicMap]) {
    // 记录已处理的主题
    processedTopic.value = topic

    const topicName = topicMap[topic as keyof typeof topicMap]
    // 自动发送一个关于该主题的问题
    const welcomeMessage = `您好！我想了解关于${topicName}的相关信息，请为我介绍一下。`

    // 添加用户消息
    messages.value.push({
      type: 'user',
      content: welcomeMessage,
      time: new Date().toLocaleTimeString()
    })

    // 模拟AI回复
    setTimeout(() => {
      const aiResponse = getTopicResponse(topic)
      messages.value.push({
        type: 'assistant',
        content: aiResponse,
        time: new Date().toLocaleTimeString()
      })
      scrollToBottom()
    }, 1000)
  }
}

// 添加聊天记录加载相关的响应式变量
const currentChatId = ref<string | null>(null)
const isLoadingHistory = ref(false)

// 加载聊天记录
const loadChatHistory = async (chatId: string) => {
  if (isLoadingHistory.value) {
    console.log('Already loading, skip...')
    return
  }
  
  isLoadingHistory.value = true
  
  try {
    console.log('Loading chat history for chatId:', chatId)
    
    // 根据不同的chatId加载不同的聊天记录
    let mockChatHistory = []
    
    if (chatId === 'chat-001') {
      mockChatHistory = [
        {
          type: 'user' as const,
          content: '请介绍一下管道施工的基本流程',
          time: '14:30',
          files: []
        },
        {
          type: 'assistant' as const,
          content: `<h1 class="message-heading message-h1">管道施工基本流程</h1>
<h2 class="message-heading message-h2">1. 施工准备阶段</h2>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">图纸审查</strong>：详细审查施工图纸和技术规范</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">材料检验</strong>：检查材料质量证明文件</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">现场确认</strong>：确认施工现场条件和安全措施</span></div>

<h2 class="message-heading message-h2">2. 施工实施阶段</h2>
<div class="message-list-item message-ordered-item"><span class="message-list-number">1.</span><span class="message-list-content">管道预制和组装</span></div>
<div class="message-list-item message-ordered-item"><span class="message-list-number">2.</span><span class="message-list-content">管道安装和连接</span></div>
<div class="message-list-item message-ordered-item"><span class="message-list-number">3.</span><span class="message-list-content"><strong class="message-bold">焊接作业</strong>：执行焊接工艺规程</span></div>
<div class="message-list-item message-ordered-item"><span class="message-list-number">4.</span><span class="message-list-content">质量检验和测试</span></div>

<h2 class="message-heading message-h2">3. 验收阶段</h2>
<blockquote class="message-quote"><strong class="message-bold">重要提醒</strong>：严格按照验收标准执行</blockquote>

<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">压力试验</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">防腐检查</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">竣工验收</span></div>

<p>💡 <strong class="message-bold">安全第一</strong>：施工过程中请严格遵守安全操作规程。</p>`,
          time: '14:31',
          files: [],
          searchSources: [
            {
              title: '12.5.3施工组织设计的内容',
              content: '土木工程施工-武汉理工大学出版社',
              category: '企业标准'
            },
            {
              title: '管道施工技术规范 GB50235-2010',
              content: '建筑工程技术标准-国家标准',
              category: '技术规范'
            },
            {
              title: '焊接工艺质量控制要点',
              content: '工程质量管理手册-企业内部标准',
              category: '质量管理'
            },
            {
              title: '安全生产操作规程',
              content: '企业安全管理制度-2024版',
              category: '安全标准'
            }
          ]
        }
      ]
    } else if (chatId === 'chat-002') {
      mockChatHistory = [
        {
          type: 'user' as const,
          content: '焊接工艺标准有哪些要求？',
          time: '15:20',
          files: []
        },
        {
          type: 'assistant' as const,
          content: `<h2 class="message-heading message-h2">⚡ 焊接工艺标准要求</h2>

<h3 class="message-heading message-h3">焊接前准备</h3>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接材料选择与验收</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接设备检查与调试</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊工资质确认</span></div>

<h3 class="message-heading message-h3">焊接过程控制</h3>
<div class="message-list-item message-ordered-item"><span class="message-list-number">1.</span><span class="message-list-content"><strong class="message-bold">工艺参数控制</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接电流：根据管径和壁厚确定</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接速度：保证熔透和成型</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">电弧电压：控制在规定范围内</span></div>

<div class="message-list-item message-ordered-item"><span class="message-list-number">2.</span><span class="message-list-content"><strong class="message-bold">质量控制要点</strong></span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊缝外观检查</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">无损检测（RT/UT）</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">焊接接头力学性能测试</span></div>

<h3 class="message-heading message-h3">安全防护措施</h3>
<p>⚠️ <strong class="message-bold">安全要求</strong>：</p>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">防触电措施</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">防火防爆措施</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content">个人防护用品佩戴</span></div>

<p><strong class="message-bold">质量标准</strong>：焊缝应符合相关标准要求，无裂纹、气孔等缺陷。</p>`,
          time: '15:21',
          files: [],
          searchSources: [
            {
              title: '管道焊接实战经验总结',
              content: '个人项目经验总结-张工程师',
              category: '个人笔记'
            },
            {
              title: '施工现场常见问题及解决方案',
              content: '个人工作日志-问题解决记录',
              category: '经验分享'
            },
            {
              title: '焊接技术学习心得',
              content: '个人技术心得-焊接专业培训笔记',
              category: '学习笔记'
            },
            {
              title: '项目管理实践总结',
              content: '个人职业发展-项目管理经验',
              category: '管理心得'
            }
          ]
        }
      ]
    } else {
      mockChatHistory = [
        {
          type: 'user' as const,
          content: '请问有什么可以帮助我的吗？',
          time: '10:00',
          files: []
        },
        {
          type: 'assistant' as const,
          content: `<h2 class="message-heading message-h2">智库问答助手</h2>
<p>您好！我是您的智能助手，可以为您提供专业的技术咨询和解答。</p>

<h3 class="message-heading message-h3">我可以帮助您：</h3>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">技术问题解答</strong>：工程技术、施工工艺等专业问题</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">标准规范查询</strong>：相关行业标准和技术规范</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">经验分享</strong>：实际工程案例和最佳实践</span></div>
<div class="message-list-item message-unordered-item"><span class="message-list-bullet">•</span><span class="message-list-content"><strong class="message-bold">问题诊断</strong>：技术难题分析和解决方案</span></div>

<p>💡 <strong class="message-bold">提示</strong>：您可以直接提出问题，我会基于知识库为您提供专业解答。</p>`,
          time: '10:01',
          files: [],
          searchSources: [
            {
              title: '工程技术标准汇编',
              content: '建筑工程技术标准-行业标准',
              category: '技术标准'
            },
            {
              title: '施工工艺指导手册',
              content: '工程施工技术-专业指南',
              category: '施工指南'
            },
            {
              title: '质量管理体系文件',
              content: '工程质量控制-管理制度',
              category: '质量管理'
            },
            {
              title: '安全操作规程',
              content: '工程安全管理-操作标准',
              category: '安全管理'
            }
          ]
        }
      ]
    }
    
    // 确保清空后再设置新内容
    messages.value = []
    await nextTick()
    messages.value = mockChatHistory
    
    console.log('Chat history loaded, message count:', messages.value.length)
    
    await nextTick()
    scrollToBottom()
    
    ElMessage.success('聊天记录加载完成')
  } catch (error) {
    ElMessage.error('加载聊天记录失败')
    console.error('Failed to load chat history:', error)
  } finally {
    isLoadingHistory.value = false
  }
}

// 处理URL参数中的聊天ID
const handleChatIdFromUrl = () => {
  const chatId = route.query.chatId as string
  
  if (chatId && chatId !== currentChatId.value) {
    currentChatId.value = chatId
    // 清空当前消息
    messages.value = []
    loadChatHistory(chatId)
  }
}
// 监听路由变化，处理chatId和topic
watch(() => route.query, (newQuery) => {
  const { chatId, topic } = newQuery
  
  console.log('Route query changed:', { chatId, topic, currentChatId: currentChatId.value })
  
  // 处理聊天ID - 优先处理
  if (chatId && chatId !== currentChatId.value) {
    console.log('Loading chat history for:', chatId)
    currentChatId.value = chatId
    processedTopic.value = null
    loadChatHistory(chatId)
    return
  }
  
  // 如果没有chatId但有topic，处理主题
  if (!chatId && topic && topic !== processedTopic.value) {
    console.log('Processing topic:', topic)
    messages.value = []
    processedTopic.value = null
    currentChatId.value = null
    handleTopicFromUrl()
    return
  }
  
  // 如果既没有chatId也没有topic，清空消息
  if (!chatId && !topic) {
    console.log('Clearing messages')
    messages.value = []
    currentChatId.value = null
    processedTopic.value = null
  }
}, { immediate: true })

// 组件挂载时获取数据
onMounted(() => {
  fetchKnowledgeCategories()
  // 初始化展开状态
  initializeExpandedState()
  // 不在这里处理主题，因为watch已经设置了immediate: true
})





// 获取当前用户头像文字
function getCurrentUserAvatar(): string {
  const currentUsername = localStorage.getItem('currentUsername')
  if (currentUsername) {
    return currentUsername.charAt(0).toUpperCase()
  }
  
  const userRole = localStorage.getItem('userRole') || 'user'
  switch (userRole) {
    case 'admin':
      return '管'
    case 'staff':
      return '员'
    default:
      return '用'
  }
}

</script>

<style scoped>
.chat-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--minimal-bg);
}

.chat-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.chat-left {
  width: 300px;
  flex-shrink: 0;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border: 1px solid #e8f2ff;
  display: flex;
  flex-direction: column;
}

.knowledge-selector {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.knowledge-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.title-line {
  width: 4px;
  height: 20px;
  background: #6366f1;
  border-radius: 2px;
  margin-right: 12px;
}

.selection-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.select-all-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.select-all-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
  background: white;
}

.select-all-checkbox:hover {
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.select-all-checkbox.checked {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.select-all-checkbox.checked:hover {
  background: #3730a3;
  border-color: #3730a3;
}

.select-all-checkbox .el-icon {
  font-size: 10px;
  font-weight: bold;
}

.select-all-text {
  font-size: 12px;
  color: #64748b;
  user-select: none;
}

.selected-count {
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
}

.clear-selection {
  font-size: 12px;
  color: #8b5cf6;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.clear-selection:hover:not(.disabled) {
  color: #7c3aed;
}

.clear-selection.disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

/* 知识库Tab页样式 */
.knowledge-tabs {
  margin-bottom: 16px;
}

.tab-buttons {
  display: flex;
  background: #e8f2ff;
  border-radius: 8px;
  padding: 4px;
  gap: 2px;
}

.tab-button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #5b7cff;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tab-button.active {
  background: white;
  color: #5b7cff;
  box-shadow: 0 1px 3px rgba(91, 124, 255, 0.2);
  font-weight: 600;
}

.tab-button:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
}

.tab-badge {
  background: #5b7cff;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.tab-button.active .tab-badge {
  background: #4f46e5;
}

/* 单一知识库类型指示器 */
.knowledge-type-indicator {
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border: 1px solid #b3d8ff;
  border-radius: 8px;
  text-align: center;
}

.type-label {
  font-size: 14px;
  font-weight: 500;
  color: #5b7cff;
}

.knowledge-search {
  display: flex;
  gap: 8px
}

.knowledge-search-input {
  flex: 1;
  padding: 0px 5px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  outline: none;
}

.knowledge-search-btn {
  padding: 12px 16px;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s ease;
}

.knowledge-search-btn:hover {
  background: #4f46e5;
}

.knowledge-search-btn.clear-btn {
  background: #f56565;
}

.knowledge-search-btn.clear-btn:hover {
  background: #e53e3e;
}

/* 知识库分类容器 */
.knowledge-categories {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding-right: 8px;
  height: calc(100vh - 200px);
  writing-mode: horizontal-tb;
  text-orientation: mixed;
}

.knowledge-categories * {
  writing-mode: horizontal-tb;
  text-orientation: mixed;
  word-break: keep-all;
}

.knowledge-categories::-webkit-scrollbar {
  width: 6px;
}

.knowledge-categories::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.knowledge-categories::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.knowledge-categories::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 一级分类容器 */
.knowledge-category {
  background: white;
  border-radius: 12px;
  overflow: visible;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  flex-shrink: 0;
}

/* 一级分类标题 */
.category-title {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #c7d2fe 0%, #a5b4fc 100%);
  font-weight: 500;
  color: #4338ca;
  font-size: 15px;
  cursor: pointer;
  border-radius: 12px 12px 0 0;
}

.category-title .expand-icon {
  margin-left: auto;
  transition: transform 0.3s ease;
  color: #4338ca;
}

.category-title .expand-icon.expanded {
  transform: rotate(90deg);
}

/* 分类列表容器 */
.category-list {
  padding: 0;
  background: white;
  border-radius: 0 0 12px 12px;
}

/* 分类项目基础样式 */
.category-item {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  border-bottom: 1px solid #f8fafc;
  white-space: nowrap;
  word-break: keep-all;
  writing-mode: horizontal-tb;
}

.category-item:hover {
  background-color: #f8fafc;
}

.category-item:last-child {
  border-bottom: none;
}

/* 复选框样式 */
.item-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
  background: white;
  transition: all 0.2s ease;
}

.item-checkbox:hover {
  border-color: #4f46e5;
}

.item-checkbox.checked {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.item-checkbox.indeterminate {
  background: #f59e0b;
  border-color: #f59e0b;
  color: white;
}

.item-checkbox .el-icon {
  font-size: 10px;
  font-weight: bold;
}

/* 项目名称 */
.item-name {
  flex: 1;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  writing-mode: horizontal-tb;
  text-orientation: mixed;
}

/* 展开图标 */
.expand-icon {
  margin-left: 8px;
  transition: transform 0.2s ease;
  cursor: pointer;
  flex-shrink: 0;
  color: #6b7280;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.chat-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.chat-right.full-width {
  width: 100%;
  max-width: none;
}

.chat-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.welcome-avatar {
  width: 120px;
  height: 120px;
  margin-bottom: 24px;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(91, 124, 255, 0.2);
}

.avatar-gif {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.welcome-text h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
  color: #333;
}

.welcome-text p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 16px;
}

.quick-questions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 600px;
}

.question-row {
  display: flex;
  gap: 12px;
}

.quick-question {
  flex: 1;
  padding: 12px 20px;
  background: var(--minimal-primary-gradient);
  border: 1px solid transparent;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.quick-question:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(91, 124, 255, 0.3);
}

.chat-input {
  width: 100%;
  margin-top: 20px;
  flex-shrink: 0;
}

.input-container {
  background: white;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.input-container:has(.uploaded-files-inline) {
  border-radius: 0 0 12px 12px;
}

.input-container:has(.uploaded-files-inline) .input-wrapper {
  border-radius: 0 0 12px 12px;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
}

.message-input {
  border: none;
  outline: none;
  padding: 16px;
  font-size: 14px;
  resize: none;
  min-height: 40px;
  max-height: 120px;
  font-family: inherit;
  border-radius: 0;
}
.message {
  display: flex;
  margin-bottom: 24px;
  align-items: flex-start;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
  margin: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar {
  background: var(--minimal-primary-gradient);
  color: white;
  font-weight: 600;
  font-size: 16px;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-gif {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  max-width: 70%;
  position: relative;
}

.message-text {
  padding: 16px 20px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.5;
  word-wrap: break-word;
}

.message.user .message-text {
  background: var(--minimal-primary-gradient);
  color: white;
  border-bottom-right-radius: 6px;
}

.message.assistant .message-text {
  background: #f1f3f5;
  color: #333;
  border-bottom-left-radius: 6px;
}

/* 消息格式化样式 */
.message-text :deep(.message-heading) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  color: #1f2937;
}

.message-text :deep(.message-h1) {
  font-size: 1.5em;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.message-text :deep(.message-h2) {
  font-size: 1.3em;
  color: #374151;
}

.message-text :deep(.message-h3) {
  font-size: 1.1em;
  color: #4b5563;
}

.message-text :deep(.message-bold) {
  font-weight: 600;
  color: #1f2937;
}

.message-text :deep(.message-italic) {
  font-style: italic;
  color: #6b7280;
}

.message-text :deep(.message-code-block) {
  background: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  margin: 8px 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.message-text :deep(.message-inline-code) {
  background: #e5e7eb;
  color: #1f2937;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.message-text :deep(.message-list-item) {
  display: flex;
  align-items: flex-start;
  margin: 4px 0;
  padding-left: 8px;
}

.message-text :deep(.message-list-number),
.message-text :deep(.message-list-bullet) {
  color: #5b7cff;
  font-weight: 600;
  margin-right: 8px;
  flex-shrink: 0;
}

.message-text :deep(.message-list-content) {
  flex: 1;
}

.message-text :deep(.message-link) {
  color: #5b7cff;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.message-text :deep(.message-link:hover) {
  border-bottom-color: #5b7cff;
}

.message-text :deep(.message-quote) {
  border-left: 4px solid #5b7cff;
  background: #f0f4ff;
  padding: 8px 12px;
  margin: 8px 0;
  font-style: italic;
  color: #374151;
}

.message-text :deep(.message-divider) {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
  margin: 16px 0;
}

.message-text :deep(.message-table) {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-text :deep(.message-table-header) {
  background: #f8f9fa;
}

.message-text :deep(.message-table-cell) {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  text-align: left;
}

.message-text :deep(.message-table-header .message-table-cell) {
  font-weight: 600;
  color: #1f2937;
}
.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.left-actions {
  display: flex;
  align-items: center;
}

.right-actions {
  display: flex;
  gap: 8px;
}

.upload-btn, .send-btn {
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
}

.send-btn:disabled {
  background: #e9ecef;
  color: #adb5bd;
  border-color: #e9ecef;
  cursor: not-allowed;
}

.input-footer {
  text-align: center;
  margin-top: 12px;
}
.footer-text {
  font-size: 12px;
  color: #adb5bd;
}
.ai-status {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 12px;
    border-radius: 16px;
  background: rgba(91, 124, 255, 0.08);
  color: #5b7cff;
    border: 1px solid rgba(91, 124, 255, 0.15);
}

.status-icon {
  font-size: 10px;
  font-weight: bold;
}

.status-text {
  font-weight: 500;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 16px 20px;
  background: #f1f3f5;
  border-radius: 18px;
  border-bottom-left-radius: 6px;
  width: fit-content;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-6px);
    opacity: 1;
  }
}
.message-actions {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  opacity: 1;
}

.action-btn {
  padding: 6px;
  background: rgba(0, 0, 0, 0.04);
  border: none;
  border-radius: 4px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.08);
  color: #475569;
  transform: scale(1.05);
}

.action-btn.delete-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.action-btn.translated {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.action-btn.translated:hover {
  background: rgba(76, 175, 80, 0.2);
  color: #2e7d32;
}

/* 翻译状态指示器 */
.translation-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #4caf50;
  margin-bottom: 8px;
  padding: 4px 8px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 12px;
  width: fit-content;
}

.action-btn svg {
  width: 14px;
  height: 14px;
}

.uploaded-files {
  margin-top: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  margin-bottom: 8px;
  border: 1px solid #e5e7eb;
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-icon {
  color: #6b7280;
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: #374151;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #9ca3af;
  flex-shrink: 0;
}

.remove-file {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 18px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.remove-file:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* 拖拽样式 */
.input-wrapper.drag-over {
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.05);
}

/* 文件标签页样式 */
.uploaded-files-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 16px 8px;
  border-bottom: none;
  border-radius: 12px 12px 0 0;
}

.file-tab {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px 8px 0 0;
  padding: 8px 12px;
  max-width: 280px;
  min-width: 200px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
}

.file-tab:hover {
  border-color: #9ca3af;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.file-tab-icon {
  flex-shrink: 0;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.file-tab-content {
  flex: 1;
  min-width: 0;
  margin-right: 8px;
}

.file-tab-title {
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.file-tab-url {
  font-size: 11px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  margin-top: 2px;
}

.file-tab-close {
  flex-shrink: 0;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.file-tab-close:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* 搜索来源样式 */
.search-sources {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid #e8f2ff;
}

.sources-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #5b7cff;
}

.sources-title {
  color: #5b7cff;
}

.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.source-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e8f2ff;
  transition: all 0.2s ease;
  cursor: pointer;
}

.source-card:hover {
  border-color: #5b7cff;
  box-shadow: 0 2px 8px rgba(91, 124, 255, 0.1);
  transform: translateY(-1px);
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.source-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.source-category {
  font-size: 11px;
  padding: 2px 6px;
  background: #e8f2ff;
  color: #5b7cff;
  border-radius: 4px;
  white-space: nowrap;
}

.source-content {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
  line-height: 1.3;
}

.source-url {
  font-size: 11px;
  color: #999;
  text-decoration: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 企业知识库来源样式 */
.source-card.enterprise-source {
  border-left: 3px solid #1890ff;
  background: linear-gradient(135deg, #f6f9ff 0%, #e8f4ff 100%);
}

.source-card.enterprise-source:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.source-category.enterprise-category {
  background: #e8f4ff;
  color: #1890ff;
  border: 1px solid #b3d8ff;
}

/* 个人知识库来源样式 */
.source-card.personal-source {
  border-left: 3px solid #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #e6f7d6 100%);
}

.source-card.personal-source:hover {
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
}

.source-category.personal-category {
  background: #e6f7d6;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 消息中的文件标签页样式 */
.message-files {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.message-file-tab {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px 12px;
  max-width: 280px;
  min-width: 200px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-file-tab:hover {
  border-color: #9ca3af;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.message-file-icon {
  flex-shrink: 0;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.message-file-content {
  flex: 1;
  min-width: 0;
  margin-right: 8px;
}

.message-file-title {
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.message-file-url {
  font-size: 11px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  margin-top: 2px;
}

.message-file-size {
  flex-shrink: 0;
  font-size: 11px;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}
/* 层级分类样式 */
.category-item-wrapper {
  width: 100%;
}

.category-item {
  display: flex;
  align-items: center;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  border-bottom: 1px solid #f8fafc;
  white-space: nowrap;
  word-break: keep-all;
  writing-mode: horizontal-tb;
  padding: 8px 20px;
}

.category-item:hover {
  background-color: #f8fafc;
}

/* 动态层级缩进 */
.category-item.level-1 { padding-left: 40px; font-weight: 500; }
.category-item.level-2 { padding-left: 60px; font-size: 13px; color: #6b7280; }
.category-item.level-3 { padding-left: 80px; font-size: 12px; color: #9ca3af; }
.category-item.level-4 { padding-left: 100px; font-size: 11px; color: #9ca3af; }
.category-item.level-5 { padding-left: 120px; font-size: 10px; color: #9ca3af; }
.category-item.level-6 { padding-left: 140px; font-size: 10px; color: #9ca3af; }
.category-item.level-7 { padding-left: 160px; font-size: 9px; color: #9ca3af; }
.category-item.level-8 { padding-left: 180px; font-size: 9px; color: #9ca3af; }

/* 复选框样式 */
.item-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
  background: white;
  transition: all 0.2s ease;
}

.item-checkbox:hover {
  border-color: #4f46e5;
}

.item-checkbox.checked {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.item-checkbox.indeterminate {
  background: #f59e0b;
  border-color: #f59e0b;
  color: white;
}

.item-checkbox .el-icon {
  font-size: 10px;
  font-weight: bold;
}

/* 层级复选框大小调整 */
.level-2 .item-checkbox {
  width: 14px;
  height: 14px;
}

.level-3 .item-checkbox {
  width: 12px;
  height: 12px;
}

.level-4 .item-checkbox {
  width: 10px;
  height: 10px;
}

.item-name {
  flex: 1;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  writing-mode: horizontal-tb;
  text-orientation: mixed;
}

.expand-icon {
  margin-left: 8px;
  transition: transform 0.2s ease;
  cursor: pointer;
  flex-shrink: 0;
  color: #6b7280;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}
</style>

<style>
/* 最高优先级的样式覆盖 */
:deep(.el-button--primary) {
  background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  background-image: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: none !important;
}

:deep(.el-button--primary:hover),
:deep(.el-button--primary:focus),
:deep(.el-button--primary:active) {
  background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  background-image: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: none !important;
}

/* 搜索来源下拉框样式 */
.search-source-select {
  border-radius: 6px;
  font-size: 12px;
}

.search-source-select .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  border-radius: 6px;
}

.search-source-select .el-input__suffix {
  height: 28px;
  line-height: 28px;
}

.search-source-select .el-select__caret {
  line-height: 28px;
}
</style>
