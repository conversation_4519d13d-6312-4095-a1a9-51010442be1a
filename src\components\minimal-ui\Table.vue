<template>
  <table class="minimal-table">
    <thead>
      <tr>
        <th v-for="col in columns" :key="col.key">{{ col.title }}</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(row, rowIndex) in data" :key="row.id || row.key || rowIndex">
        <td v-for="col in columns" :key="col.key">{{ row[col.key] }}</td>
      </tr>
    </tbody>
  </table>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

interface Column {
  key: string
  title: string
}
interface Row {
  [key: string]: any
  id?: string | number
  key?: string | number
}

defineProps<{
  columns: Column[]
  data: Row[]
}>()
</script>

<style scoped>
.minimal-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--minimal-glass-bg);
  font-size: 1rem;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.03);
  backdrop-filter: blur(var(--minimal-glass-blur));
  border: 1px solid rgba(255,255,255,0.2);
}
.minimal-table th, .minimal-table td {
  padding: 0.75em 1em;
  text-align: left;
}
.minimal-table th {
  background: rgba(91, 124, 255, 0.08);
  font-weight: 500;
  border-bottom: 1px solid rgba(91, 124, 255, 0.12);
}
.minimal-table tr:not(:last-child) td {
  border-bottom: 1px solid rgba(91, 124, 255, 0.06);
}
</style> 
