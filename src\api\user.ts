import { get, post } from '../utils/request'

// 定义接口返回数据类型
interface ResponseData<T> {
  code: number
  data: T
  message: string
}

// 用户信息接口
export interface UserInfo {
  id: number
  username: string
  nickname: string
  avatar: string
  roles: string[]
  permissions: string[]
}

// 登录参数接口
export interface LoginParams {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录接口
export function login(data: LoginParams) {
  return post<ResponseData<{ token: string; role: string }>>('/auth/login', data)
}

// 获取用户信息
export function getUserInfo() {
  return get<ResponseData<UserInfo>>('/user/info')
}

// 退出登录
export function logout() {
  return post<ResponseData<null>>('/auth/logout')
}

// 修改用户信息
export function updateUserInfo(data: Partial<UserInfo>) {
  return put<ResponseData<UserInfo>>('/user/info', data)
}
