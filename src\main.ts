import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import pinia from './store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { getAppTitle, isDevelopment } from './utils/env'
import './styles/minimal-theme.scss'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 设置页面标题
document.title = getAppTitle()

// 创建应用实例
const app = createApp(App)

// 在开发环境下启用性能分析
if (isDevelopment()) {
  app.config.performance = true
}

// 注册全局组件和插件
app.use(router)
app.use(pinia)
app.use(ElementPlus, {
  locale: zhCn,
})

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 挂载应用
app.mount('#app')

// 在开发环境下输出环境信息
if (isDevelopment()) {
  console.log('当前环境:', import.meta.env.VITE_APP_ENV)
  console.log('API 基础 URL:', import.meta.env.VITE_API_BASE_URL)
  console.log('应用版本:', import.meta.env.VITE_APP_VERSION)
}



