# 极简UI改造

## 任务背景
- 目标：所有页面采用自定义极简UI组件，逐步替换Element Plus默认组件，打造极简、统一、个性化的视觉体验。
- 现状：项目基于Element Plus，页面和组件分布于src/views、src/components（待新建）等目录。

## 详细计划

1. **需求梳理与设计规范制定**
   - 梳理现有页面涉及的所有UI组件类型（按钮、输入框、表单、卡片、表格、弹窗等）。
   - 制定极简风格设计规范（配色、字体、间距、圆角、动效、响应式等）。
   - 输出设计稿或UI参考图（如有设计师可协作，优先产出Figma/Sketch稿）。

2. **搭建自定义UI组件库基础结构**
   - 新建`/src/components/minimal-ui/`目录，作为极简组件库存放地。
   - 设计并实现基础组件（Button、Input、Card、Modal、Table等），每个组件均遵循极简规范。
   - 组件需支持响应式、主题色切换、暗色模式（如有需求）。

3. **全局样式与主题配置**
   - 新建全局样式文件（如`minimal-theme.scss`），统一基础配色、字体、背景、间距等。
   - 配置全局CSS变量，便于后续主题调整。

4. **逐步替换页面组件**
   - 按页面/模块分批，将Element Plus组件替换为自定义极简组件。
   - 替换时同步优化页面布局、内容层级、动效等，确保风格统一。
   - 保留Element Plus的功能性模块（如表单校验、弹窗逻辑），但视觉层面全部自定义。

5. **动效与交互优化**
   - 为按钮、卡片、输入框等添加克制的极简动效（如悬停、聚焦、点击反馈）。
   - 优化页面切换、弹窗弹出等过渡动画，提升整体体验。

6. **响应式与适配**
   - 确保所有自定义组件和页面在不同分辨率下均表现良好。
   - 必要时引入媒体查询或flex/grid布局优化。

7. **测试与迭代**
   - 全面测试各页面在主流浏览器、不同设备下的显示与交互。
   - 收集反馈，持续优化细节。

8. **文档与维护**
   - 为自定义组件库编写使用文档，便于团队协作与后续维护。
   - 规范组件命名、props、事件等，保持一致性。

## 首页极简改造详细步骤

1. 分析现有首页结构
   - 阅读 src/views/Dashboard.vue、Home.vue，梳理页面用到的Element Plus组件及自定义内容。
2. 设计首页极简布局
   - 参考极简风格，规划首页布局（如卡片、按钮、表格等）。
   - 明确哪些区域可用自研极简组件（minimal-ui）替换。
3. 组件替换与样式优化
   - 将首页用到的Element Plus组件（如el-button、el-input、el-card、el-table等）替换为minimal-ui下自定义组件。
   - 优化页面结构、间距、配色，确保极简统一。
   - 移除多余装饰元素，保留必要信息与交互。
4. 动效与响应式适配
   - 为首页交互元素（如按钮、卡片）添加极简动效。
   - 检查并优化首页在不同分辨率下的表现。
5. 功能验证与细节打磨
   - 确认首页功能完整，交互流畅。
   - 细节微调，确保极简风格落地。
6. 阶段性反馈
   - 完成首页改造后，提交预览和变更说明，征求反馈。

## 首页极简美化升级（参考设计图）

1. 全局渐变背景与字体优化
   - 在全局样式中实现柔和渐变背景（如淡紫/蓝/灰渐变）。
   - 优化字体（如Inter、PingFang SC等），提升轻盈感。
2. 自定义极简侧边栏
   - 新建src/components/minimal-ui/Sidebar.vue，实现扁平化侧边栏，含SVG图标+文字，选中高亮。
   - 侧边栏支持导航、选中态、悬浮动效，整体圆角、无边框。
   - 侧边栏内容可配置（如首页、统计、配置、退出等）。
3. 主内容区卡片分区
   - 首页主内容区采用大卡片分区（如统计区、图表区、数据区块等）。
   - 卡片圆角、阴影、悬浮动效，内容分明。
   - 统计区块用极简卡片展示核心数据（如天数、存活率等）。
   - 图表区块预留，后续可集成echarts等极简风格图表。
4. 极简按钮与交互元素
   - 按钮、下拉等交互元素采用自研极简组件，hover有柔和动效。
   - 统一主色调，按钮圆角、无多余装饰。
5. 首页布局与响应式
   - 首页采用flex/grid布局，保证内容居中、留白充足。
   - 适配不同分辨率，保证极简美观。
6. 细节与动效优化
   - 卡片、按钮等添加悬浮/点击动效。
   - 适当引入icon、色块点缀，提升层次。
7. 阶段性反馈
   - 首页初版美化完成后，提交预览，征求反馈，持续迭代。

---

**预期结果**：
- 所有页面视觉风格极简、统一，组件高度自定义。
- 代码结构清晰，便于维护和扩展。
- 用户体验流畅，兼容主流设备和浏览器。 