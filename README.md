# Admin System

> 基于 Vue 3 + Vite + TypeScript + Pinia + Element Plus 的现代化后台管理系统模板，适用于企业级中后台项目开发。

---

## 🚀 技术栈
- **前端框架**：Vue 3.5.x 
- **类型系统**：TypeScript 5.8.x
- **构建工具**：Vite 7.0.x
- **UI 组件库**：Element Plus 2.10.x
- **状态管理**：Pinia 3.0.x
- **路由管理**：Vue Router 4.5.x
- **HTTP 客户端**：Axios 1.10.x
- **CSS 预处理器**：SASS

## ✨ 核心特性
- 基于最新 Vue 3 生态系统构建
- 完全类型化的 TypeScript 开发体验
- 多环境配置（开发、测试、生产）
- 模块化项目结构设计
- 响应式布局，适配多端设备
- 内置完善的权限管理
- 精美 UI 组件，提升用户体验

## ⚡ 快速开始
### 环境要求
- Node.js ≥ 18.0
- 推荐使用 [pnpm](https://pnpm.io/) 作为包管理器

### 安装与运行
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建
pnpm build:test     # 测试环境
pnpm build:prod     # 生产环境

# 预览构建结果
pnpm preview:test
pnpm preview:prod
```

## 📁 项目结构
```text
├── public/              # 静态资源
├── src/                 # 源代码
│   ├── api/             # API 接口
│   ├── assets/          # 资源文件
│   ├── components/      # 公共组件
│   ├── layouts/         # 布局组件
│   ├── router/          # 路由配置
│   ├── store/           # Pinia 状态管理
│   ├── styles/          # 全局样式
│   ├── types/           # 类型定义
│   ├── utils/           # 工具函数
│   ├── views/           # 页面视图
│   ├── App.vue          # 根组件
│   └── main.ts          # 应用入口
├── .env.*               # 环境配置
├── index.html           # HTML 模板
├── tsconfig.json        # TypeScript 配置
├── vite.config.ts       # Vite 配置
└── package.json         # 依赖与脚本
```

## ❓ 常见问题（FAQ）
- **Q: 如何切换不同环境？**
  A: 修改 `.env.*` 文件并使用对应的构建/预览命令。
- **Q: 启动报错依赖未安装？**
  A: 请确保使用 pnpm 安装依赖，或删除 `node_modules` 后重新安装。
- **Q: 如何引入全局样式？**
  A: 在 `src/styles/` 目录下维护全局样式文件，并在 `main.ts` 中引入。
