<template>
  <div class="knowledge-plus-page">

    <div class="tools-container">
      <!-- 分类标签和搜索框 -->
      <div class="category-header">
        <div class="category-tabs">
          <button
            v-for="category in categories"
            :key="category.key"
            class="category-tab"
            :class="{ active: activeCategory === category.key }"
            @click="activeCategory = category.key"
          >
            {{ category.label }}
          </button>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
          <div class="search-input-wrapper">
            <svg class="search-icon" viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
              <path d="M21.71 20.29L18 16.61A9 9 0 1 0 16.61 18l3.68 3.68a1 1 0 0 0 1.42-1.42zM11 18a7 7 0 1 1 7-7 7 7 0 0 1-7 7z"/>
            </svg>
            <input
              type="text"
              v-model="searchQuery"
              placeholder="搜索工具..."
              class="search-input"
            />
            <button
              v-if="searchQuery"
              @click="clearSearch"
              class="clear-button"
            >
              <svg viewBox="0 0 24 24" width="14" height="14" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 工具网格 -->
      <div class="tools-grid">
        <div 
          v-for="tool in filteredTools" 
          :key="tool.id"
          class="tool-card"
          @click="handleToolClick(tool)"
        >
          <div class="tool-icon" :style="{ backgroundColor: tool.iconBg }">
            <span v-html="tool.icon"></span>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">{{ tool.title }}</h3>
            <p class="tool-description">{{ tool.description }}</p>
            <span class="tool-source">来自 {{ tool.source }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activeCategory = ref('efficiency')
const searchQuery = ref('')

const categories = [
  // { key: 'official', label: '官方推荐' },
  { key: 'efficiency', label: '办公提效' },
  { key: 'management', label: '管理助手' }
]

const tools = ref([
  // 智能助手类
  {
    id: 'chart-query',
    title: '图表查询',
    description: '智能查询企业制度、方案规范等文件内的图片或表格',
    source: '十一建',
    category: 'management',
    path: '/knowledge-plus/chart-query',
    icon: `<svg viewBox="0 0 24 24" width="24" height="24"><path d="M3 3v18h18M7 12l4-4 4 4 4-4" stroke="currentColor" stroke-width="2" fill="none"/></svg>`,
    iconBg: '#ff6b35'
  },
  {
    id: 'doc-assistant',
    title: '文档助手',
    description: '智能文档分析与问答，提升文档处理效率',
    source: '十一建',
    category: 'efficiency',
    path: '/knowledge-plus/doc-assistant',
    icon: `<svg viewBox="0 0 24 24" width="24" height="24"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" fill="currentColor"/></svg>`,
    iconBg: '#45b7d1'
  },
  {
    id: 'solution-assistant',
    title: '标书助手',
    description: '专业标书制作和审核辅助工具，提高投标成功率',
    source: '十一建',
    category: 'efficiency',
    path: '/knowledge-plus/solution-assistant',
    icon: `<svg viewBox="0 0 24 24" width="24" height="24"><path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" fill="none"/></svg>`,
    iconBg: '#00b894'
  },
  {
    id: 'translation-assistant',
    title: '翻译助手',
    description: '一键中英互译，直译意译，样样精通',
    source: '十一建',
    category: 'management',
    path: '/knowledge-plus/translation-assistant',
    icon: `<svg viewBox="0 0 24 24" width="24" height="24"><path d="M5 8l6 6M4 14l6-6M15 5l6 6M14 11l6-6" stroke="currentColor" stroke-width="2" fill="none"/></svg>`,
    iconBg: '#5f27cd'
  },
  {
    id: 'ocr-recognition',
    title: '图文识别',
    description: '一键上传，审核无忧，智能识别图片中的文字内容',
    source: '十一建',
    category: 'management',
    path: '/knowledge-plus/ocr-recognition',
    icon: `<svg viewBox="0 0 24 24" width="24" height="24"><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/><path d="M8 12h8M8 16h6" stroke="currentColor" stroke-width="2"/></svg>`,
    iconBg: '#00d2d3'
  },
  /* {
    id: 'knowledge-graph',
    title: '知识图谱',
    description: '可视化知识关系，构建企业知识网络',
    source: '十一建',
    category: 'management',
    path: '/knowledge-plus/knowledge-graph',
    icon: `<svg viewBox="0 0 24 24" width="24" height="24"><circle cx="12" cy="12" r="3" fill="currentColor"/><circle cx="6" cy="6" r="2" fill="currentColor"/></svg>`,
    iconBg: '#4ecdc4'
  }, */
  {
    id: 'result-matching',
    title: '成果匹配',
    description: '智能成果匹配和推荐系统，快速找到相关成果',
    source: '十一建',
    category: 'efficiency',
    path: '/knowledge-plus/result-matching',
    icon: `<svg viewBox="0 0 24 24" width="24" height="24"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" fill="none"/></svg>`,
    iconBg: '#7C3AED'
  },

])

const filteredTools = computed(() => {
  let filtered = tools.value.filter(tool => tool.category === activeCategory.value)

  // 如果有搜索查询，进一步过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(tool =>
      tool.title.toLowerCase().includes(query) ||
      tool.description.toLowerCase().includes(query) ||
      tool.source.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 清除搜索
function clearSearch() {
  searchQuery.value = ''
}

function handleToolClick(tool: any) {
  if (tool.path) {
    router.push(tool.path)
  }
}
</script>

<style scoped>
.knowledge-plus-page {
  padding: 24px;
  background: var(--minimal-bg);
}

.page-header {
  margin-bottom: 32px;
}

.tools-container {
  max-width: 1200px;
}

/* 分类头部容器 */
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.category-tabs {
  display: flex;
  gap: 8px;
}

/* 搜索容器 */
.search-container {
  display: flex;
  align-items: center;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 8px 12px;
  min-width: 280px;
  transition: all 0.2s;
}

.search-input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  color: #9ca3af;
  margin-right: 8px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #374151;
  background: transparent;
}

.search-input::placeholder {
  color: #9ca3af;
}

.clear-button {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 2px;
  margin-left: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.clear-button:hover {
  color: #6b7280;
  background: #f3f4f6;
}

.category-tab {
  padding: 12px 24px;
  border: none;
  background: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.category-tab:hover {
  color: var(--minimal-primary-gradient);
}

.category-tab.active {
  color: var(--minimal-primary-gradient);
  border-bottom-color: var(--minimal-primary-gradient);
  font-weight: 500;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.tool-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-card:hover {
  border-color: var(--minimal-primary-gradient);
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transform: translateY(-2px);
}

.tool-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.tool-content {
  flex: 1;
  min-width: 0;
}

.tool-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.tool-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.tool-source {
  font-size: 12px;
  color: #999;
}

@media (max-width: 768px) {
  .category-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .category-tabs {
    justify-content: center;
  }

  .search-input-wrapper {
    min-width: 100%;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .tool-card {
    padding: 16px;
  }
}
</style>

