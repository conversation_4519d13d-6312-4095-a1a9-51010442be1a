{"name": "admin-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:test": "vite preview --mode test", "preview:prod": "vite preview --mode production"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "lucide-vue-next": "^0.294.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.10", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "sass": "^1.69.5", "terser": "^5.24.0", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}