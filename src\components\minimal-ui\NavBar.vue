<template>
  <header class="navbar">
    <div class="navbar__content">
      <div class="navbar__title">
        <h1>管理系统</h1>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
// 简化导航栏，只保留标题
</script>

<style scoped>
.navbar {
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #f7f8fa 60%, #e9eafc 100%), var(--minimal-glass-bg);
  box-shadow: 0 2px 12px 0 rgba(91,124,255,0.06);
  border-radius: 0 0 12px 12px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  backdrop-filter: blur(var(--minimal-glass-blur));
  border-bottom: 1.5px solid #e9eafc;
}

.navbar__content {
  width: 100%;
  max-width: 1200px;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar__title h1 {
  background: var(--minimal-primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-size: 1.3em;
  font-weight: 700;
  letter-spacing: 0.04em;
  margin: 0;
}
</style> 
