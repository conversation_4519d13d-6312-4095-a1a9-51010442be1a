<template>
  <input
    class="minimal-input"
    v-bind="$attrs"
    :value="modelValue"
    @input="$emit('update:modelValue', ($event.target as HTMLInputElement)?.value)"
    :disabled="disabled"
  />
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['update:modelValue'])
</script>

<style scoped>
.minimal-input {
  width: 100%;
  padding: 0.5em 1em;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
  color: #222;
  font-size: 1rem;
  font-family: inherit;
  outline: none;
  transition: border 0.2s, box-shadow 0.2s;
}
.minimal-input:focus {
  border-color: #222;
  background: #fff;
}
.minimal-input:disabled {
  background: #f5f5f5;
  color: #aaa;
  cursor: not-allowed;
}
</style> 