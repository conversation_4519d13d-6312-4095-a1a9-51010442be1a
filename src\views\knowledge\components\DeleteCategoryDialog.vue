<template>
  <el-dialog
    v-model="visible"
    title="删除分类确认"
    width="420px"
    class="minimal-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <div class="dialog-icon delete-icon">
          <Delete />
        </div>
        <h3>删除分类确认</h3>
      </div>
    </template>
    
    <div class="dialog-content">
      <p class="confirm-message">
        确定要删除分类 <strong>"{{ categoryName }}"</strong> 吗？
      </p>
      <p class="confirm-warning">
        此操作将同时删除该分类下的所有子分类和文件，且不可恢复，请谨慎操作。
      </p>
    </div>
    
    <template #footer>
      <div class="dialog-actions">
        <el-button @click="handleCancel" class="minimal-btn minimal-btn--secondary">
          取消
        </el-button>
        <el-button @click="handleConfirm" class="minimal-btn minimal-btn--danger">
          确定删除
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Delete } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  categoryName?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
/* 极简对话框样式 */
:deep(.minimal-dialog .el-dialog) {
  background: var(--minimal-glass-bg);
  border-radius: var(--minimal-radius);
  box-shadow: 0 12px 48px 0 rgba(91, 124, 255, 0.15);
  backdrop-filter: blur(var(--minimal-glass-blur));
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

:deep(.minimal-dialog .el-dialog__header) {
  padding: 0;
  margin: 0;
}

:deep(.minimal-dialog .el-dialog__body) {
  padding: 0;
}

:deep(.minimal-dialog .el-dialog__footer) {
  padding: 0;
}

.dialog-header {
  display: flex;
  align-items: center;
  padding: 2em 2.5em 1em 2.5em;
  gap: 1em;
}

.dialog-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--minimal-primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px 0 rgba(91, 124, 255, 0.3);
}

.delete-icon {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 12px 0 rgba(239, 68, 68, 0.3);
}

.dialog-header h3 {
  font-size: 1.25em;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0;
  letter-spacing: 0.01em;
}

.dialog-content {
  padding: 0 2.5em 1.5em 2.5em;
}

.confirm-message {
  font-size: 1em;
  color: var(--minimal-text);
  margin: 0 0 0.75em 0;
  line-height: 1.5;
}

.confirm-warning {
  font-size: 0.875em;
  color: var(--minimal-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.dialog-actions {
  display: flex;
  gap: 1em;
  padding: 1.5em 2.5em 2em 2.5em;
  justify-content: flex-end;
}

.minimal-btn {
  padding: 0.75em 1.5em;
  border-radius: var(--minimal-radius);
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.minimal-btn--secondary {
  background: rgba(107, 114, 128, 0.1);
  color: var(--minimal-text-secondary);
}

.minimal-btn--secondary:hover {
  background: rgba(107, 114, 128, 0.2);
}

.minimal-btn--danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 12px 0 rgba(239, 68, 68, 0.3);
}

.minimal-btn--danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(239, 68, 68, 0.4);
}

@media (max-width: 480px) {
  :deep(.minimal-dialog .el-dialog) {
    width: 90% !important;
    margin: 1rem;
  }

  .dialog-header,
  .dialog-content,
  .dialog-actions {
    padding-left: 1.5em;
    padding-right: 1.5em;
  }

  .dialog-actions {
    flex-direction: column;
  }

  .minimal-btn {
    width: 100%;
  }
}
</style>
