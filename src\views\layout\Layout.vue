<template>
  <div class="layout">
    <Sidebar
      :items="sidebarItems"
      v-model="sidebarActive"
      :username="username"
      :user-role="currentUser.role"
      @logout="handleLogout"
      @collapsed-change="handleSidebarCollapse"
    />
    
    <main class="layout-main" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <div class="main-content">
        <router-view />
      </div>
      <!-- <footer class="layout-footer">
        <p class="copyright">© 2025 陕西建工控股集团有限公司 版权所有</p>
      </footer> -->
    </main>
    
    <ConfirmDialog
      :visible="logoutDialogVisible"
      title="退出确认"
      message="确定要退出登录吗？"
      confirm-text="确定"
      cancel-text="取消"
      @confirm="handleLogoutConfirm"
      @cancel="handleLogoutCancel"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Sidebar from '@/components/minimal-ui/Sidebar.vue'
import ConfirmDialog from '@/components/minimal-ui/ConfirmDialog.vue'
import { logout, logoutDialogVisible, handleLogoutConfirm, handleLogoutCancel } from '../../utils/logout'

const router = useRouter()
const route = useRoute()

// 获取当前登录用户信息
const getCurrentUser = () => {
  const userRole = localStorage.getItem('userRole') || 'user'

  // 从localStorage获取当前登录的用户名
  const currentUsername = localStorage.getItem('currentUsername')

  return {
    username: currentUsername,
    role: userRole
  }
}

const currentUser = getCurrentUser()
const username = currentUser.username || undefined
const sidebarActive = ref('dashboard')
const sidebarCollapsed = ref(false)

// 从本地存储获取用户菜单
const sidebarItems = computed(() => {
  const menuData = localStorage.getItem('userMenus')
  return menuData ? JSON.parse(menuData) : []
})

// 监听路由变化，更新活跃状态
watch(() => route.path, (newPath: any) => {
  console.log('路由变化:', newPath)

  // 从菜单数据中查找匹配的菜单项
  const menuData = localStorage.getItem('userMenus')
  const menus = menuData ? JSON.parse(menuData) : []

  // 递归查找匹配当前路径的菜单项（包括子菜单）
  function findMenuByPath(menuList: any[], path: string): any {
    for (const menu of menuList) {
      if (menu.path === path) {
        return menu
      }
      if (menu.children && menu.children.length > 0) {
        const childMatch = findMenuByPath(menu.children, path)
        if (childMatch) {
          return childMatch
        }
      }
    }
    return null
  }

  const matchedMenu = findMenuByPath(menus, newPath)

  if (matchedMenu) {
    console.log('找到匹配菜单:', matchedMenu)
    sidebarActive.value = matchedMenu.key
  } else {
    console.log('未找到匹配菜单，保持当前选中状态')
    // 如果没找到匹配的，检查是否是首页
    if (newPath === '/') {
      sidebarActive.value = 'dashboard'
    }
  }
}, { immediate: true })

function handleLogout() {
  logout()
}

function handleSidebarCollapse(collapsed: boolean) {
  sidebarCollapsed.value = collapsed
}
</script>

<style scoped>
.layout {
  display: flex;
  height: 100vh;
  background: var(--minimal-bg);
  overflow: hidden;
}

.layout-main {
  flex: 1;
  margin-left: 300px;
  margin-top: 20px;
  padding-right: 30px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 40px);
  overflow: hidden;
  transition: margin-left 0.2s cubic-bezier(.4,0,.2,1);
}

.layout-main.sidebar-collapsed {
  margin-left: 100px;
}

.main-content {
  height:100%;
  overflow-y: auto;
  transition: margin-right 0.2s cubic-bezier(.4,0,.2,1);
}

.layout-main.sidebar-collapsed .main-content {
  margin-right: 20px;
}

.layout-footer {
  flex-shrink: 0;
  padding: 12px 20px;
  /* border-top: 1px solid #e5e7eb; */
  background: rgba(248, 250, 252, 0.95);
  backdrop-filter: blur(10px);
}

.copyright {
  margin: 0;
  text-align: center;
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-main {
    margin-left: 0;
    margin: 10px;
    height: calc(100vh - 20px);
  }

  .layout-footer {
    padding: 10px 16px;
  }

  .copyright {
    font-size: 11px;
  }
}
</style>
