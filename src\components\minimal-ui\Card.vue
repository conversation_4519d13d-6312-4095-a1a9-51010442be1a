<template>
  <div class="minimal-card">
    <slot />
  </div>
</template>

<script setup lang="ts">
// 无需props，极简卡片
</script>

<style scoped>
.minimal-card {
  background: var(--minimal-glass-bg);
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(91,124,255,0.06);
  padding: 2.5em;
  margin: 1.5em 0;
  transition: box-shadow 0.18s, border 0.18s, transform 0.18s;
  backdrop-filter: blur(var(--minimal-glass-blur));
  border: 1.5px solid transparent;
}
.minimal-card:hover {
  box-shadow: 0 8px 32px 0 rgba(91,124,255,0.10);
  border: 1.5px solid #a685ff;
  transform: translateY(-2px) scale(1.02);
}
</style> 