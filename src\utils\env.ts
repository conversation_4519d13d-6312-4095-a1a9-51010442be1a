/**
 * 获取当前环境模式
 * @returns 环境模式
 */
export function getEnvMode(): string {
  return import.meta.env.VITE_APP_ENV || 'development'
}

/**
 * 是否为开发环境
 * @returns 是否为开发环境
 */
export function isDevelopment(): boolean {
  return getEnvMode() === 'development'
}

/**
 * 是否为测试环境
 * @returns 是否为测试环境
 */
export function isTest(): boolean {
  return getEnvMode() === 'test'
}

/**
 * 是否为预发布环境
 * @returns 是否为预发布环境
 */
export function isStaging(): boolean {
  return getEnvMode() === 'staging'
}

/**
 * 是否为生产环境
 * @returns 是否为生产环境
 */
export function isProduction(): boolean {
  return getEnvMode() === 'production'
}

/**
 * 是否启用 Mock
 * @returns 是否启用 Mock
 */
export function isMockEnabled(): boolean {
  return import.meta.env.VITE_MOCK_ENABLED === 'true'
}

/**
 * 获取 API 基础 URL
 * @returns API 基础 URL
 */
export function getApiBaseUrl(): string {
  return import.meta.env.VITE_API_BASE_URL
}

/**
 * 获取应用标题
 * @returns 应用标题
 */
export function getAppTitle(): string {
  return import.meta.env.VITE_APP_TITLE
}

/**
 * 获取应用版本
 * @returns 应用版本
 */
export function getAppVersion(): string {
  return import.meta.env.VITE_APP_VERSION
}