<script setup lang="ts">
import MinimalCard from '@/components/minimal-ui/Card.vue'
</script>

<template>
  <div class="home">
    <MinimalCard>
      <h1 style="margin:0 0 0.5em 0;">首页</h1>
      <p>欢迎进入系统首页</p>
    </MinimalCard>
  </div>
</template>

<style scoped>
.home {
  padding: 40px 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 60vh;
  background: var(--minimal-bg);
}
</style>