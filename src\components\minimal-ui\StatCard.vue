<template>
  <div class="stat-card">
    <div class="stat-icon" :style="{ color: color }">
      <slot name="icon"></slot>
    </div>
    <div class="stat-content">
      <div class="stat-value">{{ value }}</div>
      <div class="stat-desc">{{ desc }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  value: string
  desc: string
  color?: string
}

withDefaults(defineProps<Props>(), {
  color: 'var(--minimal-primary-gradient)'
})
</script>

<style scoped>
.stat-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background: var(--minimal-glass-bg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px 0 rgba(91, 124, 255, 0.15);
  border-color: rgba(91, 124, 255, 0.3);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.9;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--minimal-text);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-desc {
  font-size: 0.95rem;
  color: var(--minimal-text-secondary);
  font-weight: 500;
}
</style> 
