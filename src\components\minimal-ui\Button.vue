<template>
  <button
    :class="['minimal-btn', { 'minimal-btn--primary': type === 'primary', 'minimal-btn--disabled': disabled }]"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

defineProps({
  type: {
    type: String,
    default: 'default', // 'default' | 'primary'
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['click'])
</script>

<style scoped>
.minimal-btn {
  padding: 0.5em 1.8em;
  border: none;
  border-radius: var(--minimal-radius);
  background: var(--minimal-glass-bg);
  color: var(--minimal-primary-gradient);
  font-size: 1.08em;
  font-family: inherit;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s, border 0.18s;
  outline: none;
  box-shadow: 0 2px 8px 0 rgba(91,124,255,0.08);
  font-weight: 500;
  border: 1.5px solid transparent;
  backdrop-filter: blur(var(--minimal-glass-blur));
}
.minimal-btn--primary {
  background: var(--minimal-primary-gradient);
  color: #fff;
  box-shadow: 0 4px 16px 0 rgba(91,124,255,0.13);
}
.minimal-btn:active {
  background: #eaeaea;
}
.minimal-btn--primary:active {
  background: #6b7cff;
}
.minimal-btn--disabled,
.minimal-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.minimal-btn:hover:not(:disabled) {
  box-shadow: 0 8px 32px 0 rgba(91,124,255,0.18);
  border: 1.5px solid #a685ff;
  filter: brightness(1.04);
}
</style> 