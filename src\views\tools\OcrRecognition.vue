<template>
  <div class="ocr-recognition">
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <div class="step-circle">1</div>
        <span class="step-label">上传文件</span>
        <div class="step-line" v-if="currentStep > 1"></div>
      </div>
      <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <div class="step-circle">2</div>
        <span class="step-label">在线识别</span>
        <div class="step-line" v-if="currentStep > 2"></div>
      </div>
      <div class="step" :class="{ active: currentStep >= 3 }">
        <div class="step-circle">3</div>
        <span class="step-label">导出结果</span>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 步骤1：上传文件 -->
      <div  class="upload-container">
        <div class="upload-area" @click="triggerFileInput" @dragover.prevent @drop.prevent="handleDrop">
          <div class="upload-icon">
            <svg viewBox="0 0 64 64" width="64" height="64" fill="none">
              <rect x="12" y="8" width="32" height="44" rx="2" stroke="#D1D5DB" stroke-width="2"/>
              <path d="M20 20h16M20 28h16M20 36h12" stroke="#D1D5DB" stroke-width="2"/>
              <path d="M44 24v24" stroke="#D1D5DB" stroke-width="2"/>
              <path d="M40 28l4-4 4 4" stroke="#D1D5DB" stroke-width="2"/>
            </svg>
          </div>
          <div class="upload-text">
            <h3>点击上传文件/拖拽文件到此处</h3>
            <p>支持png、jpg、jpeg格式文件，单个文件大小不超过10M</p>
          </div>
        </div>
        <input 
          ref="fileInput" 
          type="file" 
          accept="image/png,image/jpg,image/jpeg" 
          @change="handleFileSelect"
          style="display: none"
        />
      </div>

      <!-- 步骤2&3：识别和结果 -->
      <div  class="recognition-container">
        <div class="recognition-layout">
          <!-- 左侧：在线识别 -->
          <div class="recognition-left">
            <h3 class="section-title">在线识别</h3>
            <div class="recognition-status">
              <!-- 显示上传的文件 -->
              <div v-if="uploadedFile" class="uploaded-file-display">
                <div class="file-preview">
                  <img :src="filePreviewUrl" :alt="uploadedFile.name" class="file-image" />
                </div>
                <div class="file-info">
                  <div class="file-tab">
                    <div class="file-tab-icon">
                      <svg viewBox="0 0 24 24" width="16" height="16" fill="#4285f4">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                      </svg>
                    </div>
                    <div class="file-tab-content">
                      <div class="file-tab-title">{{ uploadedFile.name.split('.')[0] }}</div>
                      <div class="file-tab-url">{{ uploadedFile.name }}</div>
                    </div>
                    <div class="file-tab-size">{{ formatFileSize(uploadedFile.size) }}</div>
                  </div>
                </div>
              </div>

              <!-- 识别状态 -->
              <div class="recognition-progress">
                <div v-if="isProcessing" class="processing">
                  <div class="loading-spinner"></div>
                  <p>正在识别中...</p>
                </div>
                <div v-else-if="uploadedFile" class="completed">
                  <div class="success-icon">✓</div>
                  <p>识别完成</p>
                </div>
                <div v-else class="waiting">
                  <p>等待上传文件...</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：识别结果 -->
          <div class="recognition-right">
            <h3 class="section-title">识别结果</h3>
            <div class="result-area">
              <textarea 
                v-model="recognitionResult"
                placeholder="识别结果将显示在这里..."
                :readonly="isProcessing"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- 底部操作区 -->
        <div class="bottom-actions">
          <div class="action-buttons">
            <button class="btn btn-copy" @click="copyResult">
              <svg viewBox="0 0 16 16" width="16" height="16">
                <path d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V2Zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H6ZM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1H2Z" fill="currentColor"/>
              </svg>
              复制
            </button>
            <button class="btn btn-export" @click="exportResult">
              <svg viewBox="0 0 16 16" width="16" height="16">
                <path d="M8.5 1.5A1.5 1.5 0 0 0 7 0H3.5A1.5 1.5 0 0 0 2 1.5v13A1.5 1.5 0 0 0 3.5 16h9a1.5 1.5 0 0 0 1.5-1.5V6.5a1.5 1.5 0 0 0-1.5-1.5h-4ZM3 1.5a.5.5 0 0 1 .5-.5H7v4.5A1.5 1.5 0 0 0 8.5 7h4.5v7.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5v-13Z" fill="currentColor"/>
              </svg>
              导出
            </button>
          </div>
          <p class="disclaimer">以上内容由AI模型生成，请您仔细核对，如有错误请及时修改</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const currentStep = ref(1)
const fileInput = ref<HTMLInputElement>()
const recognitionResult = ref('')
const isProcessing = ref(false)
const uploadedFile = ref<File | null>(null)
const filePreviewUrl = ref('')

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    handleFile(file)
  }
}

// 处理拖拽上传
const handleDrop = (event: DragEvent) => {
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    handleFile(files[0])
  }
}

// 处理文件
const handleFile = (file: File) => {
  // 验证文件类型
  const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('请上传png、jpg或jpeg格式的图片')
    return
  }

  // 验证文件大小
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10M')
    return
  }

  // 保存文件信息
  uploadedFile.value = file

  // 创建文件预览URL
  if (filePreviewUrl.value) {
    URL.revokeObjectURL(filePreviewUrl.value)
  }
  filePreviewUrl.value = URL.createObjectURL(file)

  // 进入识别阶段
  currentStep.value = 2
  isProcessing.value = true

  // 模拟OCR识别过程
  setTimeout(() => {
    recognitionResult.value = `这是模拟的OCR识别结果。

实际项目中，这里会调用OCR API进行图片文字识别。
识别的文本内容会显示在这个区域。

文件名：${file.name}
文件大小：${(file.size / 1024).toFixed(2)} KB
识别时间：${new Date().toLocaleString()}`

    isProcessing.value = false
    currentStep.value = 3
    ElMessage.success('识别完成')
  }, 2000)
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 复制结果
const copyResult = async () => {
  try {
    await navigator.clipboard.writeText(recognitionResult.value)
    ElMessage.success('复制成功')
  } catch {
    ElMessage.error('复制失败')
  }
}

// 导出结果
const exportResult = () => {
  const blob = new Blob([recognitionResult.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `OCR识别结果_${new Date().getTime()}.txt`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('导出成功')
}
</script>

<style scoped>
.ocr-recognition {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem;
}

/* 步骤指示器 */
.steps-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

.step {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  justify-content: center;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 16px;
  left: calc(50% + 60px);
  right: calc(-50% + 60px);
  height: 2px;
  background: #e5e7eb;
  z-index: 1;
}

.step.completed:not(:last-child)::after {
  background: #6366f1;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-right: 8px;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.step.active .step-circle {
  background: #6366f1;
  color: white;
}

.step.completed .step-circle {
  background: #6366f1;
  color: white;
}

.step-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  position: relative;
  z-index: 2;
}

.step.active .step-label {
  color: #374151;
}

/* 上传区域 */
.upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.upload-area {
  width: 100%;
  height: 250px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.upload-area:hover {
  border-color: #6366f1;
  background: #f8faff;
}

.upload-icon {
  margin-bottom: 1rem;
  color: #9ca3af;
}

.upload-text h3 {
  font-size: 16px;
  color: #374151;
  margin-bottom: 8px;
  text-align: center;
}

.upload-text p {
  font-size: 14px;
  color: #6b7280;
  text-align: center;
  margin: 0;
}

/* 识别和结果区域 */
.recognition-container {
  margin: 0 auto;
}

.recognition-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 16px;
  color: #374151;
  margin-bottom: 1rem;
  font-weight: 600;
}

.recognition-status {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  height: 300px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.uploaded-file-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.file-preview {
  width: 200px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
}

.file-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.file-info {
  width: 100%;
  max-width: 280px;
}

.file-tab {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-tab-icon {
  flex-shrink: 0;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.file-tab-content {
  flex: 1;
  min-width: 0;
  margin-right: 8px;
}

.file-tab-title {
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.file-tab-url {
  font-size: 11px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  margin-top: 2px;
}

.file-tab-size {
  flex-shrink: 0;
  font-size: 11px;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.recognition-progress {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.success-icon {
  width: 40px;
  height: 40px;
  background: #6366f1;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-bottom: 1rem;
}

.result-area {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  height: 300px;
}

.result-area textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
}

/* 底部操作区 */
.bottom-actions {
  text-align: center;
}

.shortcut-info {
  margin-bottom: 1rem;
}

.shortcut-badge {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  justify-content: end;
  gap: 1rem;
  margin-bottom: 1rem;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-copy {
  background: #f3f4f6;
  color: #374151;
}

.btn-copy:hover {
  background: #e5e7eb;
}

.btn-export {
  background: #6366f1;
  color: white;
}

.btn-export:hover {
  background: #5856eb;
}

.disclaimer {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
  line-height: 1.4;
}
</style>


