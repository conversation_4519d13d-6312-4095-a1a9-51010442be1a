<template>
  <el-dialog
    v-model="visible"
    width="800px"
    :before-close="handleClose"
  >
   <template #header>
      <div class="drawer-header">
        <div class="drawer-title">
          <div class="title-bar"></div>
          <span>冲突详情</span>
        </div>
      </div>
    </template>

    <div class="conflict-detail-content">
      <div class="conflict-file-info">
        <h3>{{ currentConflictFile?.filename }}</h3>
      </div>

      <div class="conflict-sections">
        <!-- 冲突文本段落1 -->
        <div class="conflict-section">
          <div class="conflict-tag conflict-tag-red">
            <span class="tag-dot"></span>
            冲突文本段落1
          </div>
          <div class="conflict-text">
            By chance or nature's changing course untrimm'd;但是的，或是随自然变化而流逝；But thy eternal summer shall not fade,
            但是你的永恒之夏不会褪色；Nor lose possession of that fair thou ow'st;你不会失去你的美丽的拥有；Nor shall Death brag thou wander'st
            in his shade,死神也不能夸耀你在他的阴影中游荡；When in eternal lines to time thou grow'st；如果你在这不朽的诗句中得到永生；So
            long as men can breathe or eyes can see,只要人们能呼吸，眼睛能看见，So long lives this, and this gives life to thee.此诗就会不朽，使
            你的生命不朽。
          </div>
          <div class="conflict-text">
            Computers are becoming a part of our everyday life. In fact, they are almost everywhere. In many countries, more and more companies
            are replacing people with computers.Telephone companies are no exception. When making a long-distance call on a pay phone in
            America, you will no longer talk to an operator. Instead, a computer will answer. It will tell you what to do. No wonder people say that
            computers are taking over the world.
          </div>
        </div>

        <!-- 冲突文本段落2 -->
        <div class="conflict-section">
          <div class="conflict-tag conflict-tag-red">
            <span class="tag-dot"></span>
            冲突文本段落2
          </div>
          <div class="conflict-text">
            By chance or nature's changing course untrimm'd;但是的，或是随自然变化而流逝；But thy eternal summer shall not fade,
            但是你的永恒之夏不会褪色；Nor lose possession of that fair thou ow'st;你不会失去你的美丽的拥有；Nor shall Death brag thou wander'st
            in his shade,死神也不能夸耀你在他的阴影中游荡；When in eternal lines to time thou grow'st；如果你在这不朽的诗句中得到永生；So
            long as men can breathe or eyes can see,只要人们能呼吸，眼睛能看见，So long lives this, and this gives life to thee.此诗就会不朽，使
            你的生命不朽。
          </div>
          <div class="conflict-text">
            Computers are becoming a part of our everyday life. In fact, they are almost everywhere. In many countries, more and more companies
            are replacing people with computers.Telephone companies are no exception. When making a long-distance call on a pay phone in
            America, you will no longer talk to an operator. Instead, a computer will answer. It will tell you what to do. No wonder people say that
            computers are taking over the world.
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  visible: boolean
  currentConflictFile?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const currentConflictFile = computed(() => props.currentConflictFile)

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.drawer-header {
  padding: 20px 24px;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.drawer-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.title-bar {
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #5b7cff 0%, #a685ff 100%);
  border-radius: 2px;
}

/* 冲突详情弹窗样式 */
.conflict-detail-content {
  padding: 20px 0;
}

.conflict-file-info h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding: 0 24px;
}

.conflict-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0 24px;
}

.conflict-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.conflict-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
}

.conflict-tag-red {
  background: #fef2f2;
  color: #dc2626;
  border-bottom: 1px solid #fecaca;
}

.tag-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.conflict-text {
  padding: 16px;
  line-height: 1.6;
  color: #374151;
  background: #fafafa;
  border-bottom: 1px solid #e5e7eb;
}

.conflict-text:last-child {
  border-bottom: none;
}
</style>
