<template>
  <div class="user-permission-page">
    <div class="page-header">
      <h1>用户权限管理</h1>
      <p>管理系统用户的角色和访问权限</p>
    </div>

    <div class="permission-content">
      <el-table :data="users" style="width: 100%">
        <el-table-column prop="id" label="用户ID" width="80" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="nickname" label="昵称" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="scope">
            <el-tag :type="getRoleType(scope.row.role)">
              {{ getRoleLabel(scope.row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLogin" label="最后登录" width="160" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editUser(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              :type="scope.row.status === 'active' ? 'danger' : 'success'"
              @click="toggleStatus(scope.row)"
            >
              {{ scope.row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const users = ref([
  {
    id: 1,
    username: 'admin',
    nickname: '系统管理员',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    lastLogin: '2024-12-09 14:30:00'
  },
  {
    id: 2,
    username: 'staff001',
    nickname: '后台人员',
    email: '<EMAIL>',
    role: 'staff',
    status: 'active',
    lastLogin: '2024-12-09 13:20:00'
  },
  {
    id: 3,
    username: 'user001',
    nickname: '普通用户',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    lastLogin: '2024-12-09 12:10:00'
  }
])

const getRoleType = (role: string) => {
  switch (role) {
    case 'admin': return 'danger'
    case 'staff': return 'warning'
    case 'user': return 'info'
    default: return 'info'
  }
}

const getRoleLabel = (role: string) => {
  switch (role) {
    case 'admin': return '管理员'
    case 'staff': return '后台人员'
    case 'user': return '用户'
    default: return '未知'
  }
}

const editUser = (user: any) => {
  ElMessage.info(`编辑用户: ${user.nickname}`)
}

const toggleStatus = (user: any) => {
  user.status = user.status === 'active' ? 'inactive' : 'active'
  ElMessage.success(`用户 ${user.nickname} 已${user.status === 'active' ? '启用' : '禁用'}`)
}
</script>

<style scoped>
.user-permission-page {
  padding: 24px;
  background: var(--minimal-bg);
}

.page-header {
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0 0 8px 0;
}

.page-header p {
  color: var(--minimal-text-secondary);
  margin: 0;
}

.permission-content {
  background: var(--minimal-glass-bg);
  border-radius: var(--minimal-radius);
  padding: 24px;
  backdrop-filter: blur(var(--minimal-glass-blur));
  border: 1px solid var(--minimal-border);
}
</style>