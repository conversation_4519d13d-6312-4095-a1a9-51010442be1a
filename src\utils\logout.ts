import { ElMessage } from 'element-plus'
import { removeToken } from './auth'
import router from '../router'
import { ref } from 'vue'

// 创建响应式状态来控制对话框
export const logoutDialogVisible = ref(false)
export const logoutDialogResolve = ref<((value: boolean) => void) | null>(null)

/**
 * 显示退出确认对话框
 */
export function showLogoutConfirm(): Promise<boolean> {
  return new Promise((resolve) => {
    logoutDialogResolve.value = resolve
    logoutDialogVisible.value = true
  })
}

/**
 * 处理对话框确认
 */
export function handleLogoutConfirm() {
  logoutDialogVisible.value = false
  if (logoutDialogResolve.value) {
    logoutDialogResolve.value(true)
    logoutDialogResolve.value = null
  }
}

/**
 * 处理对话框取消
 */
export function handleLogoutCancel() {
  logoutDialogVisible.value = false
  if (logoutDialogResolve.value) {
    logoutDialogResolve.value(false)
    logoutDialogResolve.value = null
  }
}

/**
 * 退出登录
 * @param showConfirm 是否显示确认对话框
 */
export async function logout(showConfirm: boolean = true) {
  if (showConfirm) {
    try {
      const confirmed = await showLogoutConfirm()
      if (!confirmed) {
        return
      }
    } catch {
      // 用户取消退出
      return
    }
  }

  try {
    // 移除本地 token
    removeToken()
    
    // 清理其他本地存储数据
    localStorage.removeItem('userRole')
    localStorage.removeItem('userMenus')
    localStorage.removeItem('currentUsername')
    // 注意：不清除 rememberedUsername，因为这是用于"记住我"功能的
    
    // 先跳转到登录页，再显示消息
    router.push('/login').then(() => {
      // 显示退出成功消息
      ElMessage({
        message: '退出登录成功',
        type: 'success',
        duration: 2000
      })
      console.log('已跳转到登录页')
    }).catch(error => {
      console.error('跳转登录页失败:', error)
    })
    
  } catch (error) {
    console.error('退出登录失败:', error)
    ElMessage({
      message: '退出登录失败，请重试',
      type: 'error'
    })
  }
}
