<template>
  <div class="enterprise-category">

    <!-- 搜索表单 -->
    <div class="search-form">
      <div class="form-row">
        <div class="form-item">
          <label>分类名称</label>
          <el-input
            v-model="searchForm.categoryName"
            placeholder="输入分类名称"
            clearable
          />
        </div>
        <div class="form-item">
          <label>是否启用</label>
          <el-select
            v-model="searchForm.isEnabled"
            placeholder="选择是或否"
            clearable
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </div>
        <el-button type="primary" @click="handleSearch" :icon="Search" style="background: linear-gradient(90deg, #5b7cff 0%, #a685ff 100%); margin-left: 8px;">搜索</el-button>
        <el-button @click="handleReset" :icon="Refresh" style="background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  color: #64748b;
  border-radius: 8px;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;">重置</el-button>
      </div>
    </div>
        <!-- 展开/折叠按钮 -->
    <div class="expand-toolbar">
      <el-button
        :icon="isExpanded ? ArrowDown : ArrowRight"
        @click="toggleExpand"
        plain
      >
        {{ isExpanded ? '折叠' : '展开' }}
      </el-button>
    </div>

    <!-- 分类树形结构 -->
    <div class="category-tree-container">
      <!-- 表头 -->
      <div class="tree-header">
        <div class="header-item category-col">分类</div>
        <div class="header-item status-col">是否启用</div>
        <div class="header-item date-col">创建日期</div>
        <div class="header-item action-col">操作</div>
      </div>

      <!-- 树形内容滚动容器 -->
      <div class="tree-scroll-container">
        <div class="tree-content" v-if="paginatedData.length > 0">
          <CategoryTreeItem
            v-for="item in paginatedData"
            :key="item.id"
            :item="item"
            :level="0"
            @toggle="toggleItem"
            @edit="handleEdit"
            @add="handleAdd"
            @delete="handleDelete"
          />
        </div>
      </div>
    </div>

    <!-- 分页 - 右下角固定 -->
   <!--  <div class="pagination-fixed">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="filteredData.length"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        small
      />
    </div> -->

    <!-- 分类弹窗组件 -->
    <CategoryDialog
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :is-add-child="isAddChild"
      :parent-category-name="parentCategoryName"
      :form-data="formData"
      @submit="handleDialogSubmit"
      @cancel="resetForm"
      @update:form-data="handleFormDataUpdate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Search, Refresh, ArrowDown, ArrowRight } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CategoryDialog from './components/CategoryDialog.vue'
import CategoryTreeItem from './components/CategoryTreeItem.vue'

// 搜索表单数据
const searchForm = reactive({
  categoryName: '',
  isEnabled: ''
})

// 展开折叠状态
const isExpanded = ref(false)

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isAddChild = ref(false)
const parentCategoryName = ref('')

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  isEnabled: true,
  parentId: null
})

// 表格数据 - 与个人知识库数据保持一致，添加管理字段
const categoryData = ref([
  {
    id: 'shiyi-library',
    name: '十一智库-资料',
    isEnabled: true,
    createTime: '2024-12-09 13:58:59',
    expanded: false,
    children: [
      {
        id: 'external',
        name: '外部',
        isEnabled: true,
        createTime: '2024-12-09 13:58:59',
        expanded: false,
        children: [
          {
            id: 'regulations',
            name: '规范、图集',
            isEnabled: true,
            createTime: '2024-12-09 13:58:59',
            expanded: false,
            children: [
              {
                id: 'construction-standards',
                name: '施工规范、图集',
                isEnabled: true,
                createTime: '2024-12-09 13:58:59',
                expanded: false,
                children: [
                  {
                    id: 'construction-regulations',
                    name: '施工规范',
                    isEnabled: true,
                    createTime: '2024-12-09 13:58:59',
                    expanded: false,
                    children: [
                      { id: 'national-construction', name: '国家规范', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                      { id: 'industry-construction', name: '行业规范', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                      { id: 'local-construction', name: '地方规范', isEnabled: true, createTime: '2024-12-09 13:58:59' }
                    ]
                  },
                  {
                    id: 'construction-atlas',
                    name: '施工图集',
                    isEnabled: true,
                    createTime: '2024-12-09 13:58:59',
                    expanded: false,
                    children: [
                      { id: 'national-atlas', name: '国家规范', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                      { id: 'industry-atlas', name: '行业规范', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                      { id: 'local-atlas', name: '地方规范', isEnabled: true, createTime: '2024-12-09 13:58:59' }
                    ]
                  }
                ]
              },
              {
                id: 'design-standards',
                name: '设计规范、图集',
                isEnabled: true,
                createTime: '2024-12-09 13:58:59',
                expanded: false,
                children: [
                  { id: 'electrical', name: '电', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'building', name: '建筑', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'structure', name: '结构', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'heating', name: '暖', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'water', name: '水', isEnabled: true, createTime: '2024-12-09 13:58:59' }
                ]
              }
            ]
          }
        ]
      },
      {
        id: 'internal',
        name: '内部',
        isEnabled: true,
        createTime: '2024-12-09 13:58:59',
        expanded: false,
        children: [
          {
            id: 'shaanxi-construction-holding',
            name: '陕西建工控股集团有限公司',
            isEnabled: true,
            createTime: '2024-12-09 13:58:59',
            expanded: false,
            children: [
              { id: 'holding-regulations', name: '制度', isEnabled: true, createTime: '2024-12-09 13:58:59' },
              { id: 'holding-standards', name: '标准', isEnabled: true, createTime: '2024-12-09 13:58:59' },
              { id: 'holding-policies', name: '相关办法及通知', isEnabled: true, createTime: '2024-12-09 13:58:59' }
            ]
          },
          {
            id: 'shaanxi-construction-group',
            name: '陕西建工集团股份有限公司',
            isEnabled: true,
            createTime: '2024-12-09 13:58:59',
            expanded: false,
            children: [
              { id: 'group-regulations', name: '制度', isEnabled: true, createTime: '2024-12-09 13:58:59' },
              { id: 'group-standards', name: '标准', isEnabled: true, createTime: '2024-12-09 13:58:59' },
              { id: 'group-policies', name: '相关办法及通知', isEnabled: true, createTime: '2024-12-09 13:58:59' }
            ]
          },
          {
            id: 'shaanxi-11th-construction',
            name: '陕西建工第十一建设集团有限公司',
            isEnabled: true,
            createTime: '2024-12-09 13:58:59',
            expanded: false,
            children: [
              {
                id: 'enterprise-system-compilation',
                name: '企业制度汇编',
                isEnabled: true,
                createTime: '2024-12-09 13:58:59',
                expanded: false,
                children: [
                  { id: 'business-category', name: '经营类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'production-category', name: '生产类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'safety-category', name: '安全类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'technology-achievement', name: '科技成果类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'technical-quality', name: '技术质量类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'business-affairs', name: '商务类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'finance-category', name: '财务类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'hr-management', name: '人力资源与干部管理类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'administrative', name: '行政类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'party-discipline', name: '党风廉政建设与纪检监察类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'audit-category', name: '审计类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'worker-rights', name: '职工权益保障类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'design-category', name: '设计类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'comprehensive-management', name: '综合管理类', isEnabled: true, createTime: '2024-12-09 13:58:59' }
                ]
              },
              {
                id: 'enterprise-internal-control',
                name: '企业内部控制',
                isEnabled: true,
                createTime: '2024-12-09 13:58:59'
              },
              {
                id: 'standardization-manual',
                name: '标准化手册',
                isEnabled: true,
                createTime: '2024-12-09 13:58:59'
              },
              {
                id: 'process-standards-guide',
                name: '工艺标准指南',
                isEnabled: true,
                createTime: '2024-12-09 13:58:59'
              },
              {
                id: 'professional-knowledge',
                name: '专业知识',
                isEnabled: true,
                createTime: '2024-12-09 13:58:59',
                expanded: false,
                children: [
                  {
                    id: 'tech-achievements',
                    name: '科技成果',
                    isEnabled: true,
                    createTime: '2024-12-09 13:58:59',
                    expanded: false,
                    children: [
                      { id: 'papers', name: '论文', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                      { id: 'patents', name: '专利', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                      { id: 'construction-methods', name: '工法', isEnabled: true, createTime: '2024-12-09 13:58:59' }
                    ]
                  },
                  {
                    id: 'excellent-cases',
                    name: '优秀案例',
                    isEnabled: true,
                    createTime: '2024-12-09 13:58:59',
                    expanded: false,
                    children: [
                      { id: 'enterprise-culture-type', name: '企业文化类', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                      { id: 'party-innovation-type', name: '党建创新类', isEnabled: true, createTime: '2024-12-09 13:58:59' }
                    ]
                  },
                  { id: 'case-review-results', name: '案例复盘成果', isEnabled: true, createTime: '2024-12-09 13:58:59' },
                  { id: 'international-building-standards', name: '国际建筑说明通用规范', isEnabled: true, createTime: '2024-12-09 13:58:59' }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
])



// 搜索过滤数据
const filteredData = computed(() => {
  let filtered = categoryData.value

  if (searchForm.categoryName) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchForm.categoryName.toLowerCase())
    )
  }

  if (searchForm.isEnabled !== '') {
    const isEnabled = searchForm.isEnabled === '1'
    filtered = filtered.filter(item => item.isEnabled === isEnabled)
  }

  return filtered
})

// 分页数据计算
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

// 递归查找项目
const findItemById = (items: any[], id: string): any => {
  for (const item of items) {
    if (item.id === id) {
      return item
    }
    if (item.children && item.children.length > 0) {
      const found = findItemById(item.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 切换单个项目的展开状态
const toggleItem = (id: string) => {
  const item = findItemById(categoryData.value, id)
  if (item) {
    item.expanded = !item.expanded
  }
}

// 递归设置展开状态
const setExpandedRecursively = (items: any[], expanded: boolean) => {
  items.forEach(item => {
    item.expanded = expanded
    if (item.children && item.children.length > 0) {
      setExpandedRecursively(item.children, expanded)
    }
  })
}

// 全局展开/折叠切换
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
  setExpandedRecursively(categoryData.value, isExpanded.value)
}

// 操作方法
const handleEdit = (item: any) => {
  formData.id = item.id
  formData.name = item.name
  formData.isEnabled = item.isEnabled
  formData.parentId = null

  dialogTitle.value = '编辑分类'
  isAddChild.value = false
  dialogVisible.value = true
}

const handleAdd = (item: any) => {
  formData.id = null
  formData.name = ''
  formData.isEnabled = true
  formData.parentId = item.id

  dialogTitle.value = '新增子分类'
  isAddChild.value = true
  parentCategoryName.value = item.name
  dialogVisible.value = true
}

const handleDelete = (item: any) => {
  ElMessageBox.confirm(
    `确定要删除分类 "${item.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 删除主分类
    const index = categoryData.value.findIndex(cat => cat.id === item.id)
    if (index > -1) {
      categoryData.value.splice(index, 1)
    } else {
      // 删除子分类
      for (const parent of categoryData.value) {
        const childIndex = parent.children?.findIndex((child: any) => child.id === item.id)
        if (childIndex !== undefined && childIndex > -1) {
          parent.children.splice(childIndex, 1)
          break
        }
      }
    }
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  ElMessage.success('搜索完成')
}

// 重置处理
const handleReset = () => {
  searchForm.categoryName = ''
  searchForm.isEnabled = ''
  currentPage.value = 1
  ElMessage.info('已重置搜索条件')
}

// 表单重置
const resetForm = () => {
  formData.id = null
  formData.name = ''
  formData.isEnabled = true
  formData.parentId = null
  isAddChild.value = false
  parentCategoryName.value = ''
}

// 处理表单数据更新
const handleFormDataUpdate = (data: any) => {
  Object.assign(formData, data)
}

// 处理弹窗提交
const handleDialogSubmit = (data: any) => {
  if (data.id) {
    // 编辑
    updateCategory()
  } else {
    // 新增
    addCategory()
  }
}

// 新增分类
const addCategory = () => {
  const newCategory = {
    id: Date.now().toString(),
    name: formData.name,
    isEnabled: formData.isEnabled,
    createTime: new Date().toLocaleString(),
    expanded: false,
    children: []
  }

  if (formData.parentId) {
    // 添加子分类
    const parent = categoryData.value.find(item => item.id === formData.parentId)
    if (parent) {
      parent.children.push({
        id: Date.now().toString(),
        name: formData.name,
        isEnabled: formData.isEnabled,
        createTime: new Date().toLocaleString(),
        expanded: false,
        children: []
      })
    }
  } else {
    // 添加主分类
    categoryData.value.push(newCategory)
  }

  dialogVisible.value = false
  resetForm()
  ElMessage.success('新增成功')
}

// 更新分类
const updateCategory = () => {
  const category = categoryData.value.find(item => item.id === formData.id)
  if (category) {
    category.name = formData.name
    category.isEnabled = formData.isEnabled
  } else {
    // 查找子分类
    for (const parent of categoryData.value) {
      const child = parent.children?.find((child: any) => child.id === formData.id)
      if (child) {
        child.name = formData.name
        child.isEnabled = formData.isEnabled
        break
      }
    }
  }

  dialogVisible.value = false
  resetForm()
  ElMessage.success('修改成功')
}






</script>

<style scoped>
.enterprise-category {
  height: 100%;
}

/* 搜索表单 */
.search-form {
  padding: 10px 10px 15px 0px
}

.form-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-item label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
  min-width: 80px;
}

.form-item .el-input,
.form-item .el-select {
  width: 200px;
}

.form-row .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
  margin-left: 8px;
}

/* 展开工具栏 */
.expand-toolbar {
  margin-bottom: 16px;
}

.expand-toolbar .el-button {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
  font-size: 14px;
  padding: 8px 16px;
}

.expand-toolbar .el-button:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 分类树形容器 */
.category-tree-container {
  background: white;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

/* 树形表头 */
.tree-header {
  display: flex;
  background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
  border-bottom: 1px solid #c7d2fe;
  padding: 16px 20px;
  font-weight: 600;
  color: #4338ca;
  font-size: 16px;
}

.header-item {
  display: flex;
  align-items: center;
}

.category-col {
  flex: 1;
  min-width: 200px;
}

.status-col {
  display: flex;
  width: 100px;
  justify-content: center;
}

.date-col {
  display: flex;
  width: 180px;
  justify-content: center;
}

.action-col {
  display: flex;
  width: 200px;
  justify-content: center;
}

/* 树形滚动容器 */
.tree-scroll-container {
  max-height: 600px;
  overflow-y: auto;
  border-top: 1px solid #e5e7eb;
}

.tree-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.tree-scroll-container::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.tree-scroll-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.tree-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 树形内容 */
.tree-content {
  padding: 0;
}
 
.tree-item {
  border-bottom: none;
} 

.tree-item:last-child {
  border-bottom: none;
}

.tree-row {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  transition: background-color 0.2s;
}

.tree-row:hover {
  background: #f8fafc;
}

.category-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-btn {
  color: #6b7280;
  padding: 4px;
  min-width: 24px;
}

.expand-placeholder {
  width: 24px;
  height: 24px;
  display: inline-block;
}

.category-name {
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
}

/* 子项样式 */
/* .tree-children {
  border-top: 1px solid #e5e7eb;
  background: #fafbfc;
} */
/* 
.child-item {
  border-bottom: 1px solid #e5e7eb;
} */

.child-item:last-child {
  border-bottom: none;
}

.child-row {
  padding: 12px 20px;
  /* background: #fafbfc; */
}

.child-row:hover {
  background: #f3f4f6;
}

.child-content {
  padding-left: 32px;
}

.child-name {
  font-size: 15px;
  font-weight: 400;
}

/* 表格工具栏 */
.table-toolbar {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f5f9;
  background: #fafbfc;
}

.table-toolbar .el-button {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

/* 标签样式 */
.category-tree-container :deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  padding: 4px 12px;
  border: none;
}

.category-tree-container :deep(.el-tag--primary) {
  background: #dbeafe;
  color: #1d4ed8;
}

.category-tree-container :deep(.el-tag--danger) {
  background: #fee2e2;
  color: #dc2626;
}

/* 固定分页 - 右下角 */
.pagination-fixed {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 100;
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
}

.pagination-fixed :deep(.el-pagination) {
  margin: 0;
}

.pagination-fixed :deep(.el-pagination .el-pager li) {
  background: transparent;
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.2s;
}

.pagination-fixed :deep(.el-pagination .el-pager li:hover) {
  background: #f3f4f6;
}

.pagination-fixed :deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.pagination-fixed :deep(.el-pagination .btn-prev),
.pagination-fixed :deep(.el-pagination .btn-next) {
  border-radius: 6px;
  transition: all 0.2s;
}

.pagination-fixed :deep(.el-pagination .btn-prev:hover),
.pagination-fixed :deep(.el-pagination .btn-next:hover) {
  background: #f3f4f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enterprise-category {
    padding: 16px;
  }

  .category-tabs {
    flex-wrap: wrap;
    gap: 6px;
    width: 100%;
  }

  .tab-item {
    padding: 8px 16px;
    font-size: 13px;
  }

  .action-bar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }

  .table-container {
    border-radius: 12px;
  }

  .table-container :deep(.el-table__header th),
  .table-container :deep(.el-table__body td) {
    padding: 12px 16px;
  }
}
</style>