<template>
  <teleport to="body">
    <div v-if="visible" class="minimal-confirm-mask" @click.self="handleCancel">
      <div class="minimal-confirm-dialog">
        <div class="confirm-header">
          <div class="confirm-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <h3>{{ title }}</h3>
        </div>
        <div class="confirm-content">
          <p>{{ message }}</p>
        </div>
        <div class="confirm-actions">
          <button class="minimal-btn minimal-btn--secondary" @click="handleCancel">
            {{ cancelText }}
          </button>
          <button class="minimal-btn minimal-btn--primary" @click="handleConfirm">
            {{ confirmText }}
          </button>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '确认'
  },
  message: {
    type: String,
    default: '确定要执行此操作吗？'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  }
})

const emit = defineEmits(['confirm', 'cancel'])

function handleConfirm() {
  emit('confirm')
}

function handleCancel() {
  emit('cancel')
}
</script>

<style scoped>
.minimal-confirm-mask {
  position: fixed;
  z-index: 2000;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
}

.minimal-confirm-dialog {
  background: var(--minimal-glass-bg);
  border-radius: var(--minimal-radius);
  min-width: 400px;
  max-width: 90vw;
  padding: 0;
  box-shadow: 0 12px 48px 0 rgba(91, 124, 255, 0.15);
  animation: confirmIn 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(var(--minimal-glass-blur));
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

@keyframes confirmIn {
  0% {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }
  100% {
    transform: none;
    opacity: 1;
  }
}

.confirm-header {
  display: flex;
  align-items: center;
  padding: 2em 2.5em 1em 2.5em;
  gap: 1em;
}

.confirm-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px 0 rgba(255, 107, 107, 0.3);
}

.confirm-header h3 {
  font-size: 1.25em;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0;
  letter-spacing: 0.01em;
}

.confirm-content {
  padding: 0 2.5em 1.5em 2.5em;
}

.confirm-content p {
  font-size: 1em;
  color: #7a7e8a;
  margin: 0;
  line-height: 1.6;
  font-weight: 400;
}

.confirm-actions {
  display: flex;
  gap: 1em;
  padding: 1.5em 2.5em 2em 2.5em;
  justify-content: flex-end;
}

.minimal-btn {
  padding: 0.7em 2em;
  border: none;
  border-radius: 12px;
  font-size: 0.95em;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  font-weight: 500;
  letter-spacing: 0.01em;
  min-width: 80px;
}

.minimal-btn--secondary {
  background: var(--minimal-glass-bg);
  color: var(--minimal-text);
  border: 1px solid var(--minimal-border);
  backdrop-filter: blur(8px);
}

.minimal-btn--secondary:hover {
  background: rgba(0, 0, 0, 0.05);
  border-color: var(--minimal-primary-gradient);
}

.minimal-btn--primary {
  background: var(--minimal-primary-gradient);
  color: white;
  box-shadow: 0 4px 12px 0 rgba(91, 124, 255, 0.25);
}

.minimal-btn--primary:hover {
  box-shadow: 0 6px 16px 0 rgba(91, 124, 255, 0.35);
  transform: translateY(-1px);
}

.minimal-btn--primary:active {
  transform: translateY(0);
}

@media (max-width: 480px) {
  .minimal-confirm-dialog {
    min-width: 320px;
    margin: 1rem;
  }
  
  .confirm-header,
  .confirm-content,
  .confirm-actions {
    padding-left: 1.5em;
    padding-right: 1.5em;
  }
  
  .confirm-actions {
    flex-direction: column;
  }
  
  .minimal-btn {
    width: 100%;
  }
}
</style>