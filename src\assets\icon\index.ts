// 图标导入 - 使用?raw导入SVG内容以支持currentColor
import DashboardIcon from './dashboard.svg?raw'
import ChatIcon from './chat.svg?raw'
import RecentChatsIcon from './recent-chats.svg?raw'
import PipelineConstructionIcon from './pipeline-construction.svg?raw'
import PipelineMaintenanceIcon from './pipeline-maintenance.svg?raw'
import WeldingConstructionIcon from './welding-construction.svg?raw'
import ViewMoreIcon from './view-more.svg?raw'
import KnowledgePlusIcon from './knowledge-plus.svg?raw'
import PersonalKnowledgeIcon from './personal-knowledge.svg?raw'
import TeamKnowledgeIcon from './team-knowledge.svg?raw'
import EnterpriseKnowledgeIcon from './enterprise-knowledge.svg?raw'
import SystemManagementIcon from './system-management.svg?raw'
import UserManagementIcon from './user-management.svg?raw'
import RoleManagementIcon from './role-management.svg?raw'
import PermissionManagementIcon from './permission-management.svg?raw'
import EnterpriseCategoryIcon from './enterprise-category.svg?raw'
import SystemLogIcon from './system-log.svg?raw'
import ChartQueryIcon from './chart-query.svg?raw'
import ResultMatchingIcon from './result-matching.svg?raw'
import TranslationAssistantIcon from './translation-assistant.svg?raw'
import OcrRecognitionIcon from './ocr-recognition.svg?raw'
import DocAssistantIcon from './doc-assistant.svg?raw'
import SolutionAssistantIcon from './solution-assistant.svg?raw'
import DepartmentManagementIcon from './department-management.svg?raw'
import KnowledgeGraph from './knowledge-graph.svg?raw'

// 图标映射对象
export const MenuIcons = {
  dashboard: DashboardIcon,
  chat: ChatIcon,
  recentChats: RecentChatsIcon,
  pipelineConstruction: PipelineConstructionIcon,
  pipelineMaintenance: PipelineMaintenanceIcon,
  weldingConstruction: WeldingConstructionIcon,
  viewMore: ViewMoreIcon,
  knowledgePlus: KnowledgePlusIcon,
  knowledgeGraph:KnowledgeGraph,
  personalKnowledge: PersonalKnowledgeIcon,
  teamKnowledge: TeamKnowledgeIcon,
  enterpriseKnowledge: EnterpriseKnowledgeIcon,
  systemManagement: SystemManagementIcon,
  userManagement: UserManagementIcon,
  roleManagement: RoleManagementIcon,
  permissionManagement: PermissionManagementIcon,
  enterpriseCategory: EnterpriseCategoryIcon,
  systemLog: SystemLogIcon,
  chartQuery: ChartQueryIcon,
  resultMatching: ResultMatchingIcon,
  translationAssistant: TranslationAssistantIcon,
  ocrRecognition: OcrRecognitionIcon,
  docAssistant: DocAssistantIcon,
  solutionAssistant: SolutionAssistantIcon,
  departmentManagement: DepartmentManagementIcon
}

// 图标键名类型
export type MenuIconKey = keyof typeof MenuIcons

// 获取图标SVG内容的辅助函数 - 支持动态键名
export const getMenuIcon = (iconKey: string): string => {
  const key = iconKey as MenuIconKey
  return MenuIcons[key] || MenuIcons.dashboard
}

// 检查图标是否存在
export const hasMenuIcon = (iconKey: string): boolean => {
  return iconKey in MenuIcons
}

// 获取所有可用的图标键名
export const getAvailableIconKeys = (): MenuIconKey[] => {
  return Object.keys(MenuIcons) as MenuIconKey[]
}

// 创建图标HTML函数 - 直接返回SVG内容以支持currentColor
export const createIconHTML = (iconKey: string): string => {
  const svgContent = getMenuIcon(iconKey)
  // 确保SVG使用currentColor并设置合适的尺寸
  return svgContent
    .replace(/fill="[^"]*"/g, 'fill="currentColor"')
    .replace(/stroke="[^"]*"/g, 'stroke="currentColor"')
    .replace(/<svg/, '<svg style="width: 1em; height: 1em; display: inline-block; vertical-align: middle;"')
}
