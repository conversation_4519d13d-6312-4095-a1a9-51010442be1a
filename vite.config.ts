import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'url'
// 获取当前文件的目录路径
const __dirname = fileURLToPath(new URL('.', import.meta.url))

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [vue()],
    
    // 设置别名
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    
    // 服务器选项
    server: {
      host: '0.0.0.0',
      port: 4000,
      open: env.VITE_AUTO_OPEN_BROWSER === 'true',
      proxy: {
        // 开发环境代理配置
        '/api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    
    // 构建选项
    build: {
      // 输出目录
      outDir: 'dist',
      
      // 启用/禁用 CSS 代码拆分
      cssCodeSplit: true,
      
      // 构建后是否生成 source map 文件
      sourcemap: mode !== 'production',
      
      // 设置最终构建的浏览器兼容目标
      target: 'es2015',
      
      // 指定生成静态资源的存放路径
      assetsDir: 'assets',
      
      // 小于此阈值的导入或引用资源将内联为 base64 编码
      assetsInlineLimit: 4096,
      
      // 启用/禁用 gzip 压缩大小报告
      reportCompressedSize: false,
      
      // chunk 大小警告的限制
      chunkSizeWarningLimit: 2000,
      
      // 自定义底层的 Rollup 打包配置
      rollupOptions: {
        output: {
          // 静态资源分类打包
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          
          // 拆分代码
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia'],
            elementPlus: ['element-plus'],
          },
        },
      },
      
      // 压缩选项
      minify: 'terser',
      terserOptions: {
        compress: {
          // 生产环境下移除 console 和 debugger
          drop_console: env.VITE_DROP_CONSOLE === 'true',
          drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
        },
      },
    },
    
    // CSS 相关选项
    css: {
      // 是否将 CSS 提取到单独的文件中
      // 生产环境下提取，开发环境下不提取
      extract: command === 'build',
      
      // 是否生成 CSS source maps
      devSourcemap: true,
      
      // 预处理器选项
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/styles/variables.scss";',
        },
      },
    },
    
    // 环境变量前缀
    envPrefix: 'VITE_',
  }
})