<template>
  <div class="user-bar">
    <div class="user-bar__avatar">{{ avatarText }}</div>
    <span class="user-bar__name">{{ username }}</span>
    <button class="user-bar__logout" @click="handleLogout">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
      </svg>
      退出
    </button>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue'

const props = defineProps<{ username: string }>()
const emit = defineEmits(['logout'])

const avatarText = computed(() => props.username?.charAt(0)?.toUpperCase() || 'U')

function handleLogout() {
  emit('logout')
}
</script>

<style scoped>
.user-bar {
  display: flex;
  align-items: center;
  background: transparent;
  border-radius: 2em;
  box-shadow: none;
  padding: 0 0.6em 0 0;
  gap: 0.7em;
  width: auto;
  min-width: 0;
  max-width: 100%;
  position: static;
  z-index: 1;
  backdrop-filter: none;
  border: none;
}
.user-bar__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--minimal-primary-gradient);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1em;
  font-weight: 700;
  box-shadow: 0 2px 8px 0 rgba(91,124,255,0.10);
}
.user-bar__name {
  font-size: 1em;
  color: var(--minimal-primary-gradient);
  font-weight: 500;
  letter-spacing: 0.01em;
}
.user-bar__logout {
  background: var(--minimal-primary-gradient);
  color: #fff;
  border: none;
  border-radius: 1.2em;
  padding: 0.3em 1em;
  font-size: 0.98em;
  font-weight: 500;
  cursor: pointer;
  transition: box-shadow 0.18s, filter 0.18s;
  box-shadow: 0 2px 8px 0 rgba(91,124,255,0.10);
  display: flex;
  align-items: center;
  gap: 0.4em;
}
.user-bar__logout:hover {
  box-shadow: 0 4px 16px 0 rgba(91,124,255,0.18);
  filter: brightness(1.08);
}

.user-bar__logout svg {
  width: 14px;
  height: 14px;
}
</style> 
