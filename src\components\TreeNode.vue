<template>
  <div class="tree-node">
    <div 
      :class="['tree-item', `level-${level}`]"
      @click="handleItemClick"
    >
      <div
        class="item-checkbox"
        :class="{
          checked: isChecked,
          indeterminate: isIndeterminate
        }"
        @click.stop="handleCheckboxClick"
      >
        <el-icon v-if="isChecked">
          <Check />
        </el-icon>
        <el-icon v-else-if="isIndeterminate">
          <Minus />
        </el-icon>
      </div>
      <span class="item-name">{{ node.name }}</span>
      <el-icon
        v-if="hasChildren"
        class="expand-icon"
        :class="{ expanded: isExpanded }"
        @click.stop="handleExpandClick"
      >
        <ArrowRight />
      </el-icon>
    </div>
    
    <!-- 递归渲染子节点 -->
    <div v-if="isExpanded && hasChildren" class="children-container">
      <TreeNode
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :level="level + 1"
        :selected-knowledge="selectedKnowledge"
        :expanded-items="expandedItems"
        @toggle-knowledge="$emit('toggle-knowledge', $event)"
        @toggle-expand="$emit('toggle-expand', $event)"
        @toggle-parent="$emit('toggle-parent', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElIcon } from 'element-plus'
import { Check, ArrowRight, Minus } from '@element-plus/icons-vue'

interface TreeNodeProps {
  node: {
    id: string
    name: string
    children?: TreeNodeProps['node'][]
  }
  level: number
  selectedKnowledge: string[]
  expandedItems: string[]
}

const props = defineProps<TreeNodeProps>()

const emit = defineEmits<{
  'toggle-knowledge': [id: string]
  'toggle-expand': [id: string]
  'toggle-parent': [node: TreeNodeProps['node']]
}>()

const hasChildren = computed(() => props.node.children && props.node.children.length > 0)
const isExpanded = computed(() => props.expandedItems.includes(props.node.id))

// 检查是否所有子节点都被选中
const isAllChildrenSelected = (node: TreeNodeProps['node']): boolean => {
  if (!node.children || node.children.length === 0) return false
  return node.children.every(child => {
    if (child.children && child.children.length > 0) {
      return isAllChildrenSelected(child)
    }
    return props.selectedKnowledge.includes(child.id)
  })
}

// 检查是否有部分子节点被选中
const isSomeChildrenSelected = (node: TreeNodeProps['node']): boolean => {
  if (!node.children || node.children.length === 0) return false
  return node.children.some(child => {
    if (child.children && child.children.length > 0) {
      return isSomeChildrenSelected(child) || isAllChildrenSelected(child)
    }
    return props.selectedKnowledge.includes(child.id)
  })
}

const isChecked = computed(() => {
  if (hasChildren.value) {
    return isAllChildrenSelected(props.node)
  }
  return props.selectedKnowledge.includes(props.node.id)
})

const isIndeterminate = computed(() => {
  if (!hasChildren.value) return false
  return !isAllChildrenSelected(props.node) && isSomeChildrenSelected(props.node)
})

const handleItemClick = () => {
  if (hasChildren.value) {
    emit('toggle-expand', props.node.id)
  } else {
    emit('toggle-knowledge', props.node.id)
  }
}

const handleCheckboxClick = () => {
  if (hasChildren.value) {
    emit('toggle-parent', props.node)
  } else {
    emit('toggle-knowledge', props.node.id)
  }
}

const handleExpandClick = () => {
  emit('toggle-expand', props.node.id)
}
</script>

<style scoped>
.tree-node {
  width: 100%;
}

.tree-item {
  display: flex;
  align-items: center;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  border-bottom: 1px solid #f8fafc;
  white-space: nowrap;
  padding: 8px 20px;
}

.tree-item:hover {
  background-color: #f8fafc;
}

/* 动态层级缩进 */
.tree-item.level-1 { padding-left: 40px; font-weight: 500; }
.tree-item.level-2 { padding-left: 60px; font-size: 13px; color: #6b7280; }
.tree-item.level-3 { padding-left: 80px; font-size: 12px; color: #9ca3af; }
.tree-item.level-4 { padding-left: 100px; font-size: 11px; color: #9ca3af; }
.tree-item.level-5 { padding-left: 120px; font-size: 10px; color: #9ca3af; }
.tree-item.level-6 { padding-left: 140px; font-size: 10px; color: #9ca3af; }
.tree-item.level-7 { padding-left: 160px; font-size: 9px; color: #9ca3af; }
.tree-item.level-8 { padding-left: 180px; font-size: 9px; color: #9ca3af; }

.item-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
  background: white;
  transition: all 0.2s ease;
}

.item-checkbox:hover {
  border-color: #4f46e5;
}

.item-checkbox.checked {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.item-checkbox.indeterminate {
  background: #f59e0b;
  border-color: #f59e0b;
  color: white;
}

.item-checkbox .el-icon {
  font-size: 10px;
  font-weight: bold;
}

.item-name {
  flex: 1;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expand-icon {
  margin-left: 8px;
  transition: transform 0.2s ease;
  cursor: pointer;
  flex-shrink: 0;
  color: #6b7280;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.children-container {
  width: 100%;
}
</style>
