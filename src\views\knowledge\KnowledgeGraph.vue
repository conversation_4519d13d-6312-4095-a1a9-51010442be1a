<template>
  <div class="knowledge-graph">
    <div class="graph-header">
      <div class="section-title">
        <div class="title-bar"></div>
        <h2>知识图谱</h2>
      </div>
      
      <div class="graph-controls">
        <div class="legend">
          <div class="legend-item">
            <div class="legend-color" style="background: #8B5CF6;"></div>
            <span>一级节点</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #F59E0B;"></div>
            <span>二级节点</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #3B82F6;"></div>
            <span>三级节点</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #10B981;"></div>
            <span>四级节点</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #EF4444;"></div>
            <span>五级节点</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #8B5A2B;"></div>
            <span>六级节点</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #6B7280;"></div>
            <span>七级节点</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="graph-container">
      <!-- 左侧企业库分类选择器 -->
      <div class="knowledge-selector" :class="{ collapsed: panelCollapsed }">
        <button class="collapse-btn" @click="togglePanel">
          <span :class="{ rotated: panelCollapsed }" class="collapse-icon">‹</span>
        </button>

        <div class="selector-content" v-show="!panelCollapsed">
          <!-- 知识库分类标题 -->
          <div class="knowledge-title">
            <div class="title-line"></div>
            <span>企业库分类</span>
          </div>

          <!-- 搜索框 -->
          <div class="knowledge-search">
            <input
              type="text"
              placeholder="输入分类搜索"
              class="knowledge-search-input"
              v-model="searchKeyword"
            >
            <button class="knowledge-search-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
              搜索
            </button>
          </div>

          <!-- 选择操作按钮 -->
       <!--    <div class="selection-controls">
            <div class="select-all-wrapper">
              <div
                class="select-all-checkbox"
                :class="{ checked: isAllSelected }"
                @click="toggleSelectAll"
              >
                <el-icon v-if="isAllSelected">
                  <Check />
                </el-icon>
              </div>
              <span class="select-all-text" @click="toggleSelectAll">全选</span>
            </div>

            <span class="selected-count">已选 {{ selectedKnowledge.length }} 项</span>

            <span
              class="clear-selection"
              @click="clearSelection"
              :class="{ disabled: selectedKnowledge.length === 0 }"
            >
              清除选择
            </span>
          </div>
 -->
          <!-- 知识库分类列表 -->
          <div class="knowledge-categories">
            <!-- 递归渲染分类树 -->
            <div
              v-for="category in filteredCategories"
              :key="category.id"
              class="category-tree"
            >
              <CategoryNode
                :node="category"
                :level="0"
                :selected-id="selectedKnowledge"
                :expanded-ids="expandedCategories"
                @select="handleNodeSelect"
                @toggle="handleNodeToggle"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧图谱区域 -->
      <div class="graph-right">
        <div class="graph-chart" ref="chartRef"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import * as echarts from 'echarts'
import CategoryNode from '@/components/CategoryNode.vue'

const chartRef = ref<HTMLElement>()
const panelCollapsed = ref(false)
let chartInstance: echarts.ECharts | null = null

// 知识库选择相关数据
const searchKeyword = ref('')
const selectedKnowledge = ref<string>('') // 当前选中的分类
const expandedCategories = ref<string[]>([])



// 类型定义
interface KnowledgeItem {
  id: string
  name: string
  children?: KnowledgeItem[]
}

interface KnowledgeCategory {
  id: string
  name: string
  icon: string
  children?: KnowledgeItem[]
}

// 企业库分类数据
const knowledgeCategories = ref<KnowledgeCategory[]>([
  {
    id: 'shiyi-library',
    name: '十一智库-资料',
    icon: '�',
    children: [
      {
        id: 'external',
        name: '外部',
        children: [
          {
            id: 'regulations',
            name: '规范、图集',
            children: [
              {
                id: 'construction-standards',
                name: '施工规范、图集',
                children: [
                  {
                    id: 'construction-regulations',
                    name: '施工规范',
                    children: [
                      { id: 'national-construction', name: '国家规范' },
                      { id: 'industry-construction', name: '行业规范' },
                      { id: 'local-construction', name: '地方规范' }
                    ]
                  },
                  {
                    id: 'construction-atlas',
                    name: '施工图集',
                    children: [
                      { id: 'national-atlas', name: '国家规范' },
                      { id: 'industry-atlas', name: '行业规范' },
                      { id: 'local-atlas', name: '地方规范' }
                    ]
                  }
                ]
              },
              {
                id: 'design-standards',
                name: '设计规范、图集',
                children: [
                  { id: 'electrical', name: '电' },
                  { id: 'building', name: '建筑' },
                  { id: 'structure', name: '结构' },
                  { id: 'heating', name: '暖' },
                  { id: 'water', name: '水' }
                ]
              }
            ]
          }
        ]
      },
      {
        id: 'internal',
        name: '内部',
        children: [
          {
            id: 'shaanxi-construction-holding',
            name: '陕西建工控股集团有限公司',
            children: [
              { id: 'holding-regulations', name: '制度' },
              { id: 'holding-standards', name: '标准' },
              { id: 'holding-policies', name: '相关办法及通知' }
            ]
          },
          {
            id: 'shaanxi-construction-group',
            name: '陕西建工集团股份有限公司',
            children: [
              { id: 'group-regulations', name: '制度' },
              { id: 'group-standards', name: '标准' },
              { id: 'group-policies', name: '相关办法及通知' }
            ]
          },
          {
            id: 'shaanxi-11th-construction',
            name: '陕西建工第十一建设集团有限公司',
            children: [
              {
                id: 'enterprise-system-compilation',
                name: '企业制度汇编',
                children: [
                  { id: 'business-category', name: '经营类' },
                  { id: 'production-category', name: '生产类' },
                  { id: 'safety-category', name: '安全类' },
                  { id: 'technology-achievement', name: '科技成果类' },
                  { id: 'technical-quality', name: '技术质量类' },
                  { id: 'business-affairs', name: '商务类' },
                  { id: 'finance-category', name: '财务类' },
                  { id: 'hr-management', name: '人力资源与干部管理类' },
                  { id: 'administrative', name: '行政类' },
                  { id: 'party-discipline', name: '党风廉政建设与纪检监察类' },
                  { id: 'audit-category', name: '审计类' },
                  { id: 'worker-rights', name: '职工权益保障类' },
                  { id: 'design-category', name: '设计类' },
                  { id: 'comprehensive-management', name: '综合管理类' }
                ]
              },
              {
                id: 'enterprise-internal-control',
                name: '企业内部控制'
              },
              {
                id: 'standardization-manual',
                name: '标准化手册'
              },
              {
                id: 'process-standards-guide',
                name: '工艺标准指南'
              },
              {
                id: 'professional-knowledge',
                name: '专业知识',
                children: [
                  {
                    id: 'tech-achievements',
                    name: '科技成果',
                    children: [
                      { id: 'papers', name: '论文' },
                      { id: 'patents', name: '专利' },
                      { id: 'construction-methods', name: '工法' }
                    ]
                  },
                  {
                    id: 'excellent-cases',
                    name: '优秀案例',
                    children: [
                      { id: 'enterprise-culture-type', name: '企业文化类' },
                      { id: 'party-innovation-type', name: '党建创新类' }
                    ]
                  },
                  { id: 'case-review-results', name: '案例复盘成果' },
                  { id: 'international-building-standards', name: '国际建筑说明通用规范' }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
])

// 递归获取所有叶子节点（可选择的项目）
const getAllLeafItems = (items: any[]): string[] => {
  const leafItems: string[] = []

  items.forEach(item => {
    if (item.children && item.children.length > 0) {
      // 如果有子项，递归获取子项的叶子节点
      leafItems.push(...getAllLeafItems(item.children))
    } else {
      // 如果没有子项，这就是叶子节点
      leafItems.push(item.id)
    }
  })

  return leafItems
}



// 过滤分类（根据搜索关键词）
const filteredCategories = computed(() => {
  if (!searchKeyword.value.trim()) {
    return knowledgeCategories.value
  }

  return knowledgeCategories.value.filter(category =>
    category.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})



// 处理节点选择
const handleNodeSelect = (nodeId: string) => {
  if (selectedKnowledge.value === nodeId) {
    // 如果点击的是已选中的项，则取消选择
    selectedKnowledge.value = ''
  } else {
    // 否则选择新的项
    selectedKnowledge.value = nodeId
  }
  // 直接更新图谱，不使用面包屑导航
  updateChart()
}

// 处理节点展开/收起
const handleNodeToggle = (nodeId: string) => {
  const index = expandedCategories.value.indexOf(nodeId)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(nodeId)
  }
}



// 生成当前层级的图谱数据
const generateGraphData = () => {
  const nodes: any[] = []
  const links: any[] = []

  if (!selectedKnowledge.value) {
    // 未选中任何分类：显示默认三级结构
    generateDefaultThreeLevelGraph(nodes, links)
  } else {
    // 选中了分类：显示该分类的所有层级
    const selectedCategory = findCategoryById(selectedKnowledge.value)
    if (selectedCategory) {
      generateCategoryGraph(selectedCategory, nodes, links)
    }
  }

  return {
    nodes,
    links,
    categories: [
      { name: '一级节点', itemStyle: { color: '#8B5CF6' } },
      { name: '二级节点', itemStyle: { color: '#F59E0B' } },
      { name: '三级节点', itemStyle: { color: '#3B82F6' } },
      { name: '四级节点', itemStyle: { color: '#10B981' } },
      { name: '五级节点', itemStyle: { color: '#EF4444' } },
      { name: '六级节点', itemStyle: { color: '#8B5A2B' } },
      { name: '七级节点', itemStyle: { color: '#6B7280' } }
    ]
  }
}

// 生成默认三级结构图谱
const generateDefaultThreeLevelGraph = (nodes: any[], links: any[]) => {
  knowledgeCategories.value.forEach((category) => {
    // 添加一级分类（十一智库-资料）
    nodes.push({
      id: category.id,
      name: category.name,
      category: 0, // 一级分类
      symbolSize: 100,
      itemStyle: { color: getColorByLevel(1) } // 使用一级节点颜色（紫色）
    })

    // 添加该分类下的子项（从第2级开始，默认只显示到第3级）
    if (category.children && category.children.length > 0) {
      addChildrenToGraph(category.children, nodes, links, category.id, 1, 3) // 从第2级开始，限制到第3级
    }
  })
}

// 生成选中分类的完整图谱
const generateCategoryGraph = (category: any, nodes: any[], links: any[]) => {
  // 添加当前分类作为中心节点
  nodes.push({
    id: category.id,
    name: category.name,
    category: 0, // 中心节点为一级分类
    symbolSize: 100,
    itemStyle: { color: getColorByLevel(1) } // 使用一级节点颜色（紫色）
  })

  // 添加所有子分类（不限制层级）
  if (category.children && category.children.length > 0) {
    addChildrenToGraph(category.children, nodes, links, category.id, 1) // 从第2级开始
  }
}

// 递归添加子节点到图谱（可选层级限制）
const addChildrenToGraph = (children: any[], nodes: any[], links: any[], parentId: string, level: number, maxLevel?: number) => {
  // 如果设置了最大层级限制，则检查
  if (maxLevel && level > maxLevel) return

  children.forEach((child) => {
    nodes.push({
      id: child.id,
      name: child.name,
      category: level, // 直接使用level作为分类索引，一级=0，二级=1，三级=2...
      symbolSize: Math.max(60 - level * 10, 30),
      itemStyle: { color: getColorByLevel(level + 1) } // level+1 因为函数内部使用level-1
    })

    links.push({
      source: parentId,
      target: child.id
    })

    // 如果有子项且未达到层级限制，继续添加
    if (child.children && child.children.length > 0) {
      const shouldContinue = maxLevel ? level < maxLevel : true
      if (shouldContinue) {
        addChildrenToGraph(child.children, nodes, links, child.id, level + 1, maxLevel)
      }
    }
  })
}

// 根据ID查找分类
const findCategoryById = (id: string): any => {
  const findInItems = (items: any[]): any => {
    for (const item of items) {
      if (item.id === id) return item
      if (item.children && item.children.length > 0) {
        const found = findInItems(item.children)
        if (found) return found
      }
    }
    return null
  }

  for (const category of knowledgeCategories.value) {
    if (category.id === id) return category
    if (category.children && category.children.length > 0) {
      const found = findInItems(category.children)
      if (found) return found
    }
  }

  return null
}



// 根据层级获取颜色
const getColorByLevel = (level: number): string => {
  const colors = [
    '#8B5CF6', // 一级：紫色
    '#F59E0B', // 二级：橙色
    '#3B82F6', // 三级：蓝色
    '#10B981', // 四级：绿色
    '#EF4444', // 五级：红色
    '#8B5A2B', // 六级：棕色
    '#6B7280'  // 七级：灰色
  ]
  return colors[level - 1] || colors[colors.length - 1]
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  const graphData = generateGraphData()

  const option = {
    backgroundColor: 'transparent',
    animationDurationUpdate: 1500,
    animationEasingUpdate: 'quinticInOut' as any,
    series: [{
      type: 'graph',
      layout: 'force',
      data: graphData.nodes,
      links: graphData.links,
      categories: graphData.categories,
      roam: true,
      focusNodeAdjacency: true,
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1,
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{b}',
        fontSize: 12,
        fontWeight: 'normal'
      },
      lineStyle: {
        color: 'source',
        curveness: 0.3,
        opacity: 0.6
      },
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          width: 10
        }
      },
      force: {
        repulsion: 1000,
        gravity: 0.1,
        edgeLength: [50, 200],
        layoutAnimation: true
      }
    }]
  }

  chartInstance.setOption(option, true) // true表示不合并，完全替换

  // 添加点击事件
  chartInstance.off('click') // 先移除之前的事件
  chartInstance.on('click', (params: any) => {
    if (params.dataType === 'node') {
      const nodeId = params.data.id
      // 点击图谱节点，效果与点击左侧分类相同
      handleNodeSelect(nodeId)
    }
  })
}





const togglePanel = () => {
  panelCollapsed.value = !panelCollapsed.value

  // 延迟图表重绘，等待CSS动画完成
  if (chartInstance) {
    // 立即触发一次resize，然后在动画结束后再次触发
    chartInstance.resize()
    setTimeout(() => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }, 300)
  }
}

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)

    // 初始化显示根级图谱
    updateChart()

    // 响应式处理
    const handleResize = () => {
      chartInstance?.resize()
    }
    window.addEventListener('resize', handleResize)
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', () => {})
})
</script>

<style scoped>
.knowledge-graph {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--minimal-bg);
}

.graph-header {
  padding: 1.5rem 3rem 1.5rem 0;
  border-bottom: 1px solid rgba(91, 124, 255, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-bar {
  width: 4px;
  height: 20px;
  background: var(--minimal-primary-gradient);
  border-radius: 2px;
  margin-right: 12px;
}

.section-title h2 {
  font-size: 1.3em;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0;
}

.graph-controls {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.legend {
  display: flex;
  gap: 1.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--minimal-text);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.graph-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.graph-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}



.knowledge-selector {
  width: 380px;
  min-width: 380px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-right: 1px solid rgba(91, 124, 255, 0.08);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  transition: min-width 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  max-height: 100%;
}

.knowledge-selector.collapsed {
  min-width: 48px;
  width: 48px;
  padding: 1rem 0.5rem;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-right: 2px solid #e2e8f0;
}

.selector-content {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.15s ease, transform 0.15s ease;
  min-height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.knowledge-selector.collapsed .selector-content {
  opacity: 0;
  transform: translateX(-10px);
  pointer-events: none;
}

/* 知识库标题样式 */
.knowledge-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.title-line {
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 2px;
}

/* 搜索框样式 */
.knowledge-search {
  display: flex;
  gap: 8px;
}

.knowledge-search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  outline: none;
  transition: border-color 0.2s ease;
}

.knowledge-search-input:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.knowledge-search-btn {
  padding: 8px 12px;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s ease;
}

.knowledge-search-btn:hover {
  background: #5856eb;
}

/* 当前选中项显示样式 */
.current-selection {
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-bottom: 20px;
}

.selection-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 6px;
}

.selection-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.selection-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e40af;
  flex: 1;
}

.clear-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.clear-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 选择控制按钮样式 */
.selection-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  font-size: 14px;
}

.select-all-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.select-all-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: white;
}

.select-all-checkbox.checked {
  background: #6366f1;
  border-color: #6366f1;
  color: white;
}

.select-all-text {
  font-weight: 500;
  color: #374151;
}

.selected-count {
  color: #6b7280;
  font-size: 13px;
}

.clear-selection {
  color: #ef4444;
  cursor: pointer;
  font-size: 13px;
  transition: color 0.2s ease;
}

.clear-selection:hover {
  color: #dc2626;
}

.clear-selection.disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

.collapse-btn {
  position: absolute;
  top: 1rem;
  right: 0.5rem;
  width: 28px;
  height: 28px;
  border: none;
  background: #5B7CFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(91, 124, 255, 0.3);
  font-size: 16px;
  font-weight: bold;
}

.collapse-btn:hover {
  background: #4A6BEF;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(91, 124, 255, 0.4);
}

.collapse-icon {
  transition: transform 0.3s ease;
  display: inline-block;
}

.collapse-icon.rotated {
  transform: rotate(180deg);
}

/* 知识库分类列表样式 */
.knowledge-categories {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding-right: 8px;
  height: calc(100vh - 300px);
}



.knowledge-categories::-webkit-scrollbar {
  width: 6px;
}

.knowledge-categories::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.knowledge-categories::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.knowledge-categories::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.graph-chart {
  flex: 1;
  height: 100%;
  transition: width 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0); /* 启用GPU加速 */
}

@media (max-width: 768px) {
  .graph-header {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .graph-container {
    flex-direction: column;
  }

  .knowledge-selector {
    width: 100%;
    flex-direction: row;
    padding: 1rem;
    height: auto;
    max-height: 300px;
  }

  .knowledge-categories {
    height: 200px;
  }

  .legend {
    flex-wrap: wrap;
    gap: 1rem;
  }
}
</style>






























