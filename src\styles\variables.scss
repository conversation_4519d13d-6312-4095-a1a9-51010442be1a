// 主题颜色
$primary-color: #3a7bd5;
$primary-light-color: #00d2ff;
$primary-gradient: linear-gradient(to right, $primary-color, $primary-light-color);

// 文字颜色
$text-primary: rgba(0, 0, 0, 0.87);
$text-secondary: rgba(0, 0, 0, 0.6);
$text-disabled: rgba(0, 0, 0, 0.38);

// 背景颜色
$bg-color: #ffffff;
$bg-light: #f5f7fa;
$bg-dark: #242424;

// 边框颜色
$border-color: #e4e7ed;
$border-light: #ebeef5;

// 状态颜色
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 尺寸
$header-height: 60px;
$sidebar-width: 220px;
$sidebar-collapsed-width: 64px;

// 断点
$breakpoint-xs: 576px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1600px;

// 阴影
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 4px 16px rgba(0, 0, 0, 0.15);
$box-shadow-dark: 0 8px 24px rgba(0, 0, 0, 0.2);

// 圆角
$border-radius-small: 2px;
$border-radius-base: 4px;
$border-radius-large: 8px;
$border-radius-circle: 50%;

// 动画
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;
$transition-slow: all 0.5s ease;

// 字体
$font-family-base: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
$font-size-small: 12px;
$font-size-base: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;
$font-size-xlarge: 20px;
$font-size-xxlarge: 24px;

// z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;