<template>
  <div class="recent-chats">
    <div class="chat-header">
      <div class="section-title">
        <div class="title-bar"></div>
        <h2>近期对话</h2>
      </div>
    </div>
    
    <div class="chat-list">
      <div v-if="recentChats.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg viewBox="0 0 24 24" width="48" height="48">
            <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </div>
        <h3>暂无对话记录</h3>
        <p>开始您的第一次对话吧</p>
      </div>
      
      <div v-for="chat in recentChats" :key="chat.id" class="chat-item" @click="openChat(chat.id)">
        <div class="chat-info">
          <h3 class="chat-title">{{ chat.title }}</h3>
          <p class="chat-preview">{{ chat.lastMessage }}</p>
          <div class="chat-meta">
            <span class="chat-time">{{ formatTime(chat.updatedAt) }}</span>
            <span class="chat-count">{{ chat.messageCount }} 条消息</span>
          </div>
        </div>
        <div class="chat-actions">
          <button @click.stop="deleteChat(chat.id)" class="delete-btn">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6" stroke="currentColor" stroke-width="2" fill="none"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
    
    <div class="quick-actions">
      <button @click="startNewChat" class="new-chat-btn">
        <svg viewBox="0 0 24 24" width="20" height="20">
          <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" fill="none"/>
        </svg>
        新建对话
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

interface ChatRecord {
  id: string
  title: string
  lastMessage: string
  updatedAt: Date
  messageCount: number
}

const recentChats = ref<ChatRecord[]>([
  {
    id: 'chat-001',
    title: '管道施工流程咨询',
    lastMessage: '管道施工的基本流程包括：1. 施工准备 2. 管道安装...',
    updatedAt: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
    messageCount: 8
  },
  {
    id: 'chat-002', 
    title: '焊接工艺标准查询',
    lastMessage: '焊接工艺控制要点：焊接参数设定、焊接顺序安排...',
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
    messageCount: 12
  },
  {
    id: 'chat-003',
    title: '管道维修技术讨论', 
    lastMessage: '管道维修的关键步骤包括：故障诊断、维修方案制定...',
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
    messageCount: 15
  },
  {
    id: 'chat-004',
    title: '施工安全规范咨询',
    lastMessage: '施工现场安全管理要求：个人防护用品佩戴、安全标识设置...',
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2天前
    messageCount: 6
  }
])

function formatTime(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

function openChat(chatId: string) {
  // 跳转到智库问答页面并传递聊天ID，清除其他参数
  router.push({ 
    path: '/chat', 
    query: { chatId } 
  })
}

function deleteChat(chatId: string) {
  const index = recentChats.value.findIndex(chat => chat.id === chatId)
  if (index > -1) {
    recentChats.value.splice(index, 1)
  }
}

function startNewChat() {
  router.push('/chat')
}

onMounted(() => {
  // 这里可以从后端加载真实的对话记录
})
</script>

<style scoped>
.recent-chats {
  display: flex;
  flex-direction: column;
  /* height: 100vh; */
  overflow: hidden;
}

.chat-header {
  padding: 2.5rem 3rem 1.5rem 3rem;
  background: var(--minimal-bg);
  border-bottom: 1px solid rgba(91, 124, 255, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
}

.title-bar {
  width: 4px;
  height: 20px;
  background: var(--minimal-primary-gradient);
  border-radius: 2px;
  margin-right: 12px;
}

.section-title h2 {
  font-size: 1.3em;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 3rem;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--minimal-text-secondary);
}

.empty-icon {
  margin-bottom: 1rem;
  color: var(--minimal-primary-gradient);
  opacity: 0.6;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: var(--minimal-text);
}

.chat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  margin-bottom: 1rem;
  background: var(--minimal-glass-bg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(91, 124, 255, 0.15);
  border-color: rgba(91, 124, 255, 0.25);
}

.chat-info {
  flex: 1;
}

.chat-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--minimal-text);
  margin: 0 0 0.5rem 0;
}

.chat-preview {
  color: var(--minimal-text-secondary);
  margin: 0 0 0.8rem 0;
  line-height: 1.4;
}

.chat-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--minimal-text-secondary);
  opacity: 0.8;
}

.chat-actions {
  display: flex;
  gap: 0.5rem;
}

.delete-btn {
  padding: 0.5rem;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: var(--minimal-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.quick-actions {
  padding: 1.5rem 3rem 2rem 3rem;
  border-top: 1px solid rgba(91, 124, 255, 0.08);
  background: var(--minimal-bg);
}

.new-chat-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background: var(--minimal-primary-gradient);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(91, 124, 255, 0.3);
}
</style>


