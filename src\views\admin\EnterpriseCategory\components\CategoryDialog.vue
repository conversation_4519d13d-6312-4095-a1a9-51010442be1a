<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="emit('update:visible', $event)"
    :title="title"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="category-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="category-form"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input
          :model-value="formData.name"
          @update:model-value="updateFormData('name', $event)"
          placeholder="请输入分类名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="是否启用" prop="isEnabled">
        <el-switch
          :model-value="formData.isEnabled"
          @update:model-value="updateFormData('isEnabled', $event)"
        />
      </el-form-item>

      <el-form-item label="父级分类" prop="parentId" v-if="isAddChild">
        <el-input :model-value="parentCategoryName" disabled />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="handleSubmit" class="submit-btn">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

interface CategoryFormData {
  id: number | null
  name: string
  isEnabled: boolean
  parentId: number | null
}

interface Props {
  visible: boolean
  title: string
  isAddChild: boolean
  parentCategoryName: string
  formData: CategoryFormData
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'submit', data: CategoryFormData): void
  (e: 'cancel'): void
  (e: 'update:formData', data: CategoryFormData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref()

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

// 更新表单数据
const updateFormData = (key: keyof CategoryFormData, value: any) => {
  const newFormData = { ...props.formData, [key]: value }
  emit('update:formData', newFormData)
}

// 监听visible变化，重置表单验证
watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    })
  }
})

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 处理取消
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 处理提交
const handleSubmit = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      emit('submit', props.formData)
    } else {
      ElMessage.error('请检查表单输入')
    }
  })
}
</script>

<style scoped>
.category-dialog :deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.category-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
  margin: 0;
}

.category-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.category-dialog :deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 24px;
}

.category-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 18px;
}

.category-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.category-form {
  margin-top: 8px;
}

.category-form :deep(.el-form-item__label) {
  color: #374151;
  font-weight: 500;
}

.category-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.category-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.category-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.category-form :deep(.el-switch) {
  --el-switch-on-color: #667eea;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0 24px 24px;
}

.cancel-btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  border: 1px solid #d1d5db;
  color: #6b7280;
  background: white;
  transition: all 0.2s;
}

.cancel-btn:hover {
  border-color: #9ca3af;
  color: #374151;
}

.submit-btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.2s;
}

.submit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}
</style>
