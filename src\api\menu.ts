import { get } from '../utils/request'
import { processMenuData, type BackendMenuItem } from '@/utils/menuUtils'

// 定义接口返回数据类型
interface ResponseData<T> {
  code: number
  data: T
  message: string
}

// 获取用户菜单权限
export function getUserMenus(userRole: string) {
  // 这里应该调用后端API
  // return get<ResponseData<BackendMenuItem[]>>(`/user/menus?role=${userRole}`)
  
  // 目前使用模拟数据
  return new Promise<ResponseData<BackendMenuItem[]>>((resolve) => {
    setTimeout(() => {
      let backendMenus: BackendMenuItem[] = []

      if (userRole === 'adminss') {
        // 管理员看到所有菜单
        backendMenus = [
          {
            key: 'dashboard',
            label: '首页',
            path: '/',
            iconKey: 'dashboard'
          },
          {
            key: 'user-permission',
            label: '角色管理',
            path: '/user-permission',
            iconKey: 'roleManagement'
          },
          {
            key: 'chat',
            label: '智库问答',
            path: '/chat',
            iconKey: 'chat'
          },
          {
            key: 'knowledge-plus',
            label: '智库+',
            path: '/knowledge-plus',
            iconKey: 'knowledgePlus',
            children: [
              {
                key: 'chart-query',
                label: '图表查询',
                path: '/knowledge-plus/chart-query',
                iconKey: 'chartQuery'
              },
              {
                key: 'result-matching',
                label: '成果匹配',
                path: '/knowledge-plus/result-matching',
                iconKey: 'resultMatching'
              },
              {
                key: 'translation-assistant',
                label: '翻译助手',
                path: '/knowledge-plus/translation-assistant',
                iconKey: 'translationAssistant'
              },
              {
                key: 'ocr-recognition',
                label: '图文识别',
                path: '/knowledge-plus/ocr-recognition',
                iconKey: 'ocrRecognition'
              },
              {
                key: 'doc-assistant',
                label: '文档助手',
                path: '/knowledge-plus/doc-assistant',
                iconKey: 'docAssistant'
              },
              {
                key: 'solution-assistant',
                label: '标书助手',
                path: '/knowledge-plus/solution-assistant',
                iconKey: 'solutionAssistant'
              }
            ]
          },
          {
            key: 'knowledge-graph',
            label: '知识图谱',
            path: '/knowledge-plus/knowledge-graph',
            iconKey: 'knowledgeGraph'
          },
          {
            key: 'personal-knowledge',
            label: '个人知识库',
            path: '/personal-knowledge',
            iconKey: 'personalKnowledge'
          },
          {
            key: 'recent-chats-parent',
            label: '近期对话',
            iconKey: 'recentChats',
            children: [
              {
                key: 'pipeline-construction',
                label: '管道施工',
                path: '/chat?topic=pipeline-construction'
              },
              {
                key: 'pipeline-maintenance',
                label: '管道维修',
                path: '/chat?topic=pipeline-maintenance'
              },
              {
                key: 'welding-construction',
                label: '焊接施工注意',
                path: '/chat?topic=welding-construction'
              },
              {
                key: 'recent-chats',
                label: '查看更多',
                path: '/recent-chats'
              }
            ]
          }
        ]
      } else if (userRole === 'staff') {
        // 后台人员菜单
        backendMenus = [
          {
            key: 'dashboard',
            label: '首页',
            path: '/',
            iconKey: 'dashboard'
          },
          {
            key: 'enterprise-category',
            label: '企业库分类',
            path: '/enterprise-category',
            iconKey: 'enterpriseCategory'
          },
          {
            key: 'enterprise-knowledge',
            label: '企业知识库',
            path: '/enterprise-knowledge',
            iconKey: 'enterpriseKnowledge'
          },
          {
            key: 'user-management',
            label: '用户管理',
            path: '/user-management',
            iconKey: 'userManagement'
          },
          {
            key: 'department-management',
            label: '部门管理',
            path: '/department-management',
            iconKey: 'departmentManagement'
          },
          {
            key: 'role-management',
            label: '角色管理',
            path: '/role-management',
            iconKey: 'roleManagement'
          }
        ]
      } else {
        // 普通用户菜单
        backendMenus = [
          {
            key: 'chat',
            label: '智库问答',
            path: '/chat',
            iconKey: 'chat'
          },
          {
            key: 'knowledge-plus',
            label: '智库+',
            path: '/knowledge-plus',
            iconKey: 'knowledgePlus',
            children: [
              {
                key: 'chart-query',
                label: '图表查询',
                path: '/knowledge-plus/chart-query',
                iconKey: 'chartQuery'
              },
              {
                key: 'result-matching',
                label: '成果匹配',
                path: '/knowledge-plus/result-matching',
                iconKey: 'resultMatching'
              },
              {
                key: 'translation-assistant',
                label: '翻译助手',
                path: '/knowledge-plus/translation-assistant',
                iconKey: 'translationAssistant'
              },
              {
                key: 'ocr-recognition',
                label: '图文识别',
                path: '/knowledge-plus/ocr-recognition',
                iconKey: 'ocrRecognition'
              },
              {
                key: 'doc-assistant',
                label: '文档助手',
                path: '/knowledge-plus/doc-assistant',
                iconKey: 'docAssistant'
              },
              {
                key: 'solution-assistant',
                label: '标书助手',
                path: '/knowledge-plus/solution-assistant',
                iconKey: 'solutionAssistant'
              }
            ]
          },
          {
            key: 'knowledge-graph',
            label: '知识图谱',
            path: '/knowledge-plus/knowledge-graph',
            iconKey: 'knowledgeGraph'
          },
          {
            key: 'personal-knowledge',
            label: '个人知识库',
            path: '/personal-knowledge',
            iconKey: 'personalKnowledge'
          },
          {
            key: 'recent-chats-parent',
            label: '近期对话',
            iconKey: 'recentChats',
            children: [
              {
                key: 'pipeline-construction',
                label: '管道施工',
                path: '/chat?topic=pipeline-construction'
              },
              {
                key: 'pipeline-maintenance',
                label: '管道维修',
                path: '/chat?topic=pipeline-maintenance'
              },
              {
                key: 'welding-construction',
                label: '焊接施工注意',
                path: '/chat?topic=welding-construction'
              },
              {
                key: 'recent-chats',
                label: '查看更多',
                path: '/recent-chats'
              }
            ]
          }
        ]
      }

      resolve({
        code: 200,
        data: backendMenus,
        message: '获取菜单成功'
      })
    }, 500) // 模拟网络延迟
  })
}