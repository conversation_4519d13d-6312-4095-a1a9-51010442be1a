/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
  readonly VITE_APP_ENV: 'development' | 'test' | 'staging' | 'production'
  readonly VITE_API_BASE_URL: string
  readonly VITE_MOCK_ENABLED: string
  readonly VITE_AUTO_OPEN_BROWSER: string
  readonly VITE_DROP_CONSOLE: string
  readonly VITE_DROP_DEBUGGER: string
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}