<script setup lang="ts">
import { ref } from 'vue'

const settings = ref({
  siteName: '管理系统',
  theme: 'light',
  notifications: true
})

const saveSettings = () => {
  // 保存设置逻辑
  alert('设置已保存')
}
</script>

<template>
  <div class="settings">
    <h1>系统设置</h1>
    <div class="settings-form">
      <div class="form-item">
        <label>网站名称</label>
        <input type="text" v-model="settings.siteName" />
      </div>
      <div class="form-item">
        <label>主题</label>
        <select v-model="settings.theme">
          <option value="light">浅色</option>
          <option value="dark">深色</option>
        </select>
      </div>
      <div class="form-item">
        <label>
          <input type="checkbox" v-model="settings.notifications" />
          启用通知
        </label>
      </div>
      <button @click="saveSettings">保存设置</button>
    </div>
  </div>
</template>

<style scoped>
.settings {
  padding: 20px;
}
.settings-form {
  margin-top: 20px;
  max-width: 500px;
}
.form-item {
  margin-bottom: 15px;
}
.form-item label {
  display: block;
  margin-bottom: 5px;
}
.form-item input[type="text"],
.form-item select {
  width: 100%;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
button {
  padding: 10px 20px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>